#!/usr/bin/env python3
"""
Enhanced Backtesting System with <PERSON>s, PyArrow, and AsyncIO
- Uses polars instead of pandas and pyarrow instead of numpy
- Implements asyncio with chunking for faster processing and memory optimization
- Processes individual symbol files from data/features/ as input
- Creates individual output files in data/backtest/ in parquet format with best compression
- Includes all required performance metrics columns

🚀 ADVANCED FEATURES:
🔁 1. Multi-Strategy & Multi-Timeframe Backtesting
🧠 2. Smart Backtesting Modes (Deterministic, Probabilistic, Adaptive AI)
🧪 3. Detailed Performance Metrics Calculation
🧰 4. Capital & Risk Modeling
🗂️ 5. Scenario-Based & Regime-Based Testing
🧬 6. Parameter Sweep & Optimization
🧾 7. Result Logging & Versioning
📊 8. Backtest Visualization & Debugging
🔍 9. Signal Debugging & Replay
🤖 10. LLM-Explainable Results Summary
"""

import os
import logging
import yaml
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
import asyncio
import concurrent.futures
from pathlib import Path
import gc
import time
import warnings
from typing import List, Dict, Any, Optional, <PERSON><PERSON>, Union
import hashlib
from datetime import datetime, timedelta
import tempfile
import threading
import psutil
import multiprocessing as mp
from functools import partial
import traceback
import json
import uuid
import sys
from dataclasses import dataclass, asdict
from enum import Enum
import itertools
from collections import defaultdict, deque
import numpy as np
from scipy import stats
import random
import copy

warnings.filterwarnings('ignore')

# Try to import GPU acceleration libraries
try:
    import cupy as cp
    import cudf
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

# ═══════════════════════════════════════════════════════════════════════════════
# [ENUMS] ADVANCED FEATURE ENUMS
# ═══════════════════════════════════════════════════════════════════════════════

class BacktestMode(Enum):
    """Backtesting execution modes"""
    DETERMINISTIC = "deterministic"
    PROBABILISTIC = "probabilistic"
    ADAPTIVE_AI = "adaptive_ai"
    MONTE_CARLO = "monte_carlo"
    WALK_FORWARD = "walk_forward"

class MarketRegime(Enum):
    """Market regime classification"""
    BULL = "bull"
    BEAR = "bear"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    TRENDING = "trending"
    RANGING = "ranging"

class OptimizationMethod(Enum):
    """Parameter optimization methods"""
    GRID_SEARCH = "grid_search"
    GENETIC_ALGORITHM = "genetic_algorithm"
    BAYESIAN = "bayesian"
    RANDOM_SEARCH = "random_search"
    PARTICLE_SWARM = "particle_swarm"

class RiskModel(Enum):
    """Risk modeling approaches"""
    FIXED_POSITION = "fixed_position"
    KELLY_CRITERION = "kelly_criterion"
    VOLATILITY_TARGET = "volatility_target"
    RISK_PARITY = "risk_parity"
    MAX_DRAWDOWN_TARGET = "max_drawdown_target"

# ═══════════════════════════════════════════════════════════════════════════════
# [DATACLASSES] ADVANCED FEATURE DATA STRUCTURES
# ═══════════════════════════════════════════════════════════════════════════════

@dataclass
class BacktestConfig:
    """Comprehensive backtesting configuration"""
    mode: BacktestMode = BacktestMode.DETERMINISTIC
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    initial_capital: float = 100000
    risk_model: RiskModel = RiskModel.FIXED_POSITION
    max_position_size: float = 0.1  # 10% of capital
    transaction_costs: float = 0.001  # 0.1%
    slippage: float = 0.0005  # 0.05%
    enable_regime_analysis: bool = True
    enable_correlation_analysis: bool = True
    monte_carlo_runs: int = 1000
    confidence_level: float = 0.95

@dataclass
class StrategyMetadata:
    """Enhanced strategy metadata"""
    name: str
    description: str = ""
    category: str = "momentum"
    timeframes: List[str] = None
    min_data_points: int = 100
    lookback_period: int = 252
    parameters: Dict[str, Any] = None
    risk_parameters: Dict[str, Any] = None
    regime_preferences: List[MarketRegime] = None

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    # Basic metrics
    total_return: float = 0.0
    annualized_return: float = 0.0
    volatility: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0

    # Risk metrics
    max_drawdown: float = 0.0
    max_drawdown_duration: int = 0
    var_95: float = 0.0
    cvar_95: float = 0.0

    # Trade metrics
    total_trades: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    expectancy: float = 0.0

    # Advanced metrics
    information_ratio: float = 0.0
    treynor_ratio: float = 0.0
    jensen_alpha: float = 0.0
    beta: float = 0.0
    correlation_to_market: float = 0.0

    # Regime-specific metrics
    regime_performance: Dict[str, float] = None
    regime_exposure: Dict[str, float] = None

@dataclass
class BacktestResult:
    """Comprehensive backtest result"""
    strategy_name: str
    symbol: str
    timeframe: str
    config: BacktestConfig
    metrics: PerformanceMetrics
    trades: List[Dict[str, Any]]
    equity_curve: pl.DataFrame
    regime_analysis: Dict[str, Any]
    correlation_analysis: Dict[str, Any]
    execution_time: float
    timestamp: datetime
    version: str = "1.0"

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] CONFIGURATION SECTION
# ═══════════════════════════════════════════════════════════════════════════════

def load_config():
    """Load configuration from YAML file"""
    import yaml
    import os

    # Try multiple possible config paths
    possible_paths = [
        "config/enhanced_backtesting_config.yaml",
        "../config/enhanced_backtesting_config.yaml",
        os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "enhanced_backtesting_config.yaml")
    ]

    config_data = None
    config_path = None

    for path in possible_paths:
        try:
            if os.path.exists(path):
                config_path = path
                with open(path, 'r', encoding='utf-8') as file:
                    config_data = yaml.safe_load(file)
                break
        except Exception:
            continue

    if config_data:
        # Extract relevant configuration
        general = config_data.get('general', {})
        result_logging = config_data.get('result_logging', {})

        return {
            'data_directory': general.get('data_directory', '../data/features'),
            'output_directory': general.get('output_directory', '../data/backtest'),
            'strategies_config': general.get('strategies_config', '../config/strategies.yaml'),
            'output_format': result_logging.get('output_format', 'parquet'),
            'compression': result_logging.get('compression', 'brotli'),
            'file_naming': result_logging.get('file_naming', {}),
        }
    else:
        # Return default configuration when no config file found
        return {
            'data_directory': '../data/features',
            'output_directory': '../data/backtest',
            'strategies_config': '../config/strategies.yaml',
            'output_format': 'parquet',
            'compression': 'brotli',
            'file_naming': {},
        }

# Load configuration
CONFIG = load_config()

# Data Configuration - Fix paths for running from agents directory
import os
def fix_path(path):
    """Fix relative paths when running from agents directory"""
    # Check if we're running from agents directory
    if os.path.basename(os.getcwd()) == 'agents':
        # Always use parent directory paths when running from agents
        parent_path = f"../{path.lstrip('../')}"
        if os.path.exists(parent_path):
            return parent_path
        else:
            # Create the directory in parent if it doesn't exist
            os.makedirs(parent_path, exist_ok=True)
            return parent_path
    else:
        # Running from root directory
        if os.path.exists(path):
            return path
        else:
            os.makedirs(path, exist_ok=True)
            return path

DATA_DIR = fix_path(CONFIG['data_directory'])
STRATEGIES_FILE = fix_path(CONFIG['strategies_config'])

# Output Configuration
OUTPUT_DIR = fix_path(CONFIG['output_directory'])
OUTPUT_FORMAT = CONFIG['output_format']
COMPRESSION = CONFIG['compression']

# Processing Configuration
CHUNK_SIZE = 500000         # Increased chunk size for better throughput
BATCH_SIZE = 10             # Larger symbol batches
MAX_SYMBOLS = None          # None = all symbols, or set number for testing
MAX_STRATEGIES = None       # None = all strategies, or set number for testing

# Parallel Processing Settings
CPU_COUNT = mp.cpu_count()
CONCURRENT_STRATEGIES = min(16, CPU_COUNT)  # Scale with CPU cores
CONCURRENT_SYMBOLS = min(8, CPU_COUNT // 2)  # Use half cores for symbols
MAX_WORKERS_RR = min(8, CPU_COUNT)         # Max workers for R:R ratio processing
USE_MULTIPROCESSING = True  # Enable multiprocessing for CPU-intensive tasks
PROCESS_POOL_SIZE = min(CPU_COUNT - 1, 8)  # Reserve 1 core for main process
USE_GPU_ACCELERATION = True  # Enable GPU acceleration when available

# Risk Management Configuration
RISK_REWARD_RATIOS = [[1, 1.5], [1, 2], [1.5, 2], [2, 3]]
RISK_PER_TRADE_PCT = 1.0     # Risk 1% of capital per trade
INTRADAY_MARGIN_MULTIPLIER = 3.5  # Intraday margin available (3.5x)
PROFIT_THRESHOLD = 1.0       # Minimum ROI% to consider profitable
TRANSACTION_COST_PCT = 0.05  # Transaction cost for Indian markets
SLIPPAGE_PCT = 0.02         # Slippage per trade
INITIAL_CAPITAL = 100000    # Starting capital

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] MAIN FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(message)s')
logger = logging.getLogger(__name__)

# Log GPU availability after logger is initialized
if GPU_AVAILABLE:
    logger.info("[INIT] GPU acceleration available (CuPy/CuDF)")
else:
    logger.info("[WARN] GPU acceleration not available")

# ═══════════════════════════════════════════════════════════════════════════════
# 🧹 MEMORY MANAGEMENT FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def aggressive_memory_cleanup():
    """Perform aggressive memory cleanup to prevent performance degradation"""
    try:
        # Clear polars cache
        pl.clear_cache()

        # Reset PyArrow memory pools
        pa.default_memory_pool().release_unused()

        # Force Python garbage collection
        gc.collect()

        # Log memory usage
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        logger.debug(f"🧹 Memory cleanup completed. Current usage: {memory_mb:.1f} MB")

    except Exception as e:
        logger.debug(f"Memory cleanup warning: {e}")

def reset_polars_state():
    """Reset polars internal state to prevent accumulation"""
    try:
        # Clear all polars caches
        pl.clear_cache()

        # Force garbage collection of polars objects
        gc.collect()

    except Exception as e:
        logger.debug(f"Polars state reset warning: {e}")

# Global lock for file operations to prevent concurrent writes
_file_write_lock = threading.Lock()

# Global process pool for CPU-intensive tasks
_process_pool = None

def init_process_pool():
    """Initialize the global process pool"""
    global _process_pool
    if _process_pool is None and USE_MULTIPROCESSING:
        _process_pool = mp.Pool(PROCESS_POOL_SIZE)
        logger.info(f"[CONFIG] Initialized process pool with {PROCESS_POOL_SIZE} workers")

def cleanup_process_pool():
    """Cleanup the global process pool"""
    global _process_pool
    if _process_pool is not None:
        _process_pool.close()
        _process_pool.join()
        _process_pool = None
        logger.info("🧹 Cleaned up process pool")

def extract_symbol_and_timeframe_from_filename(filename: str) -> Tuple[Optional[str], Optional[str]]:
    """Extract symbol and timeframe from filename like 'features_360ONE_1min.parquet'"""
    try:
        # Remove extension
        name_without_ext = filename.replace('.parquet', '')
        
        # Expected pattern: features_SYMBOL_TIMEFRAME or SYMBOL_TIMEFRAME
        if name_without_ext.startswith('features_'):
            # Remove 'features_' prefix
            remaining = name_without_ext[9:]  # len('features_') = 9
        else:
            remaining = name_without_ext
        
        # Split by underscore and find timeframe pattern
        parts = remaining.split('_')
        
        # Look for timeframe patterns
        timeframe_patterns = ['1min', '5min', '15min', '30min', '1hr', '1h', '4hr', '4h', '1day', '1d']
        
        timeframe = None
        symbol_parts = []
        
        for i, part in enumerate(parts):
            if part in timeframe_patterns:
                timeframe = part
                symbol_parts = parts[:i]
                break
        
        if timeframe and symbol_parts:
            symbol = '_'.join(symbol_parts)
            return symbol, timeframe
        else:
            # Fallback: assume last part is timeframe, rest is symbol
            if len(parts) >= 2:
                symbol = '_'.join(parts[:-1])
                timeframe = parts[-1]
                return symbol, timeframe
    
    except Exception as e:
        logger.debug(f"Failed to extract symbol/timeframe from {filename}: {e}")
    
    return None, None

def get_available_feature_files() -> List[Tuple[str, str, str]]:
    """Get all available feature files and extract symbols and timeframes"""
    feature_files = []

    for file_path in Path(DATA_DIR).glob("*.parquet"):
        filename = file_path.name
        
        # Extract symbol and timeframe from filename
        symbol, timeframe = extract_symbol_and_timeframe_from_filename(filename)
        
        if symbol and timeframe:
            feature_files.append((str(file_path), symbol, timeframe))
            logger.info(f"[SUCCESS] Found feature file: {filename} -> Symbol: {symbol}, Timeframe: {timeframe}")
        else:
            logger.warning(f"[WARN] Skipping file with unrecognized pattern: {filename}")
            continue

    return feature_files

def generate_output_filename(symbol: str, timeframe: str) -> str:
    """Generate output filename for individual symbol backtest results"""
    return f"backtest_{symbol}_{timeframe}.parquet"

def find_exit_vectorized_polars(df: pl.DataFrame, entry_idx: int, signal_type: int,
                               profit_target: float, stop_loss: float, timeframe: str) -> Optional[Tuple[float, int, str]]:
    """Ultra-fast exit finding using pure polars vectorized operations"""
    try:
        # Get data after entry point
        future_data = df.slice(entry_idx + 1, min(100, len(df) - entry_idx - 1))  # Limit lookforward

        if len(future_data) == 0:
            return None

        # Vectorized exit conditions using polars
        if signal_type == 1:  # Long position
            exit_conditions = future_data.with_columns([
                # Profit target hit
                (pl.col("high") >= profit_target).alias("profit_hit"),
                # Stop loss hit
                (pl.col("low") <= stop_loss).alias("stop_hit"),
                # End of day exit (simplified)
                pl.lit(True).alias("eod_exit")
            ])
        else:  # Short position
            exit_conditions = future_data.with_columns([
                # Profit target hit
                (pl.col("low") <= profit_target).alias("profit_hit"),
                # Stop loss hit
                (pl.col("high") >= stop_loss).alias("stop_hit"),
                # End of day exit
                pl.lit(True).alias("eod_exit")
            ])

        # Find first exit using polars operations
        exits = exit_conditions.with_columns([
            pl.int_range(pl.len()).alias("period"),
            pl.when(pl.col("profit_hit")).then(pl.lit("profit"))
            .when(pl.col("stop_hit")).then(pl.lit("stop"))
            .otherwise(pl.lit("eod")).alias("exit_reason")
        ])

        # Get first valid exit
        first_exit = exits.filter(
            pl.col("profit_hit") | pl.col("stop_hit") | (pl.col("period") >= 20)  # Max 20 periods
        ).head(1)

        if len(first_exit) == 0:
            return None

        exit_row = first_exit.row(0, named=True)
        holding_period = exit_row['period'] + 1
        exit_reason = exit_row['exit_reason']

        # Determine exit price
        if exit_reason == "profit":
            exit_price = profit_target
        elif exit_reason == "stop":
            exit_price = stop_loss
        else:  # EOD
            exit_price = exit_row['close']

        return exit_price, holding_period, exit_reason

    except Exception as e:
        logger.debug(f"Vectorized exit finding failed: {e}")
        return None

def calculate_trade_pnl_fast(signal_type: int, entry_price: float, exit_price: float,
                           quantity: float, position_value: float) -> Tuple[float, float]:
    """Fast PnL calculation using vectorized operations"""
    try:
        # Calculate raw PnL
        if signal_type == 1:  # Long
            trade_pnl = (exit_price - entry_price) * quantity
        else:  # Short
            trade_pnl = (entry_price - exit_price) * quantity

        # Calculate percentage return
        pnl_pct = (trade_pnl / position_value) * 100 if position_value > 0 else 0

        # Apply transaction costs (vectorized)
        transaction_cost = position_value * (TRANSACTION_COST_PCT / 100)
        trade_pnl -= transaction_cost
        pnl_pct -= TRANSACTION_COST_PCT

        return trade_pnl, pnl_pct

    except Exception:
        return 0.0, 0.0

def calculate_performance_metrics_gpu(trades: List[Dict[str, Any]], symbol: str, strategy_name: str,
                                    timeframe: str, rr_combo: List[float]) -> Optional[Dict[str, Any]]:
    """GPU-accelerated performance metrics calculation using CuPy"""
    try:
        # Skip GPU in multiprocessing workers due to CUDA context issues
        if not trades or len(trades) == 0 or not GPU_AVAILABLE or not USE_GPU_ACCELERATION:
            return calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr_combo)

        # Check if we're in a multiprocessing worker (CUDA context issues)
        import multiprocessing
        if multiprocessing.current_process().name != 'MainProcess':
            return calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr_combo)

        # Convert trades to CuPy arrays for GPU computation
        pnl_values = cp.array([t['pnl'] for t in trades])
        pnl_pct_values = cp.array([t['pnl_pct'] for t in trades])
        holding_periods = cp.array([t['holding_period'] for t in trades])

        total_trades = len(trades)

        # GPU-accelerated calculations
        winning_trades = cp.sum(pnl_values > 0).item()
        losing_trades = total_trades - winning_trades

        total_pnl = cp.sum(pnl_values).item()
        total_pnl_pct = cp.sum(pnl_pct_values).item()

        # Accuracy and expectancy
        accuracy = (winning_trades / total_trades) if total_trades > 0 else 0
        expectancy = total_pnl / total_trades if total_trades > 0 else 0

        # GPU-accelerated statistical calculations
        avg_win = cp.mean(pnl_values[pnl_values > 0]).item() if winning_trades > 0 else 0
        avg_loss = cp.mean(pnl_values[pnl_values < 0]).item() if losing_trades > 0 else 0

        # Profit factor
        gross_profit = cp.sum(pnl_values[pnl_values > 0]).item() if winning_trades > 0 else 0
        gross_loss = abs(cp.sum(pnl_values[pnl_values < 0]).item()) if losing_trades > 0 else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        # Max drawdown calculation (GPU accelerated)
        cumulative_pnl = cp.cumsum(pnl_values)
        running_max = cp.maximum.accumulate(cumulative_pnl)
        drawdowns = running_max - cumulative_pnl
        max_drawdown = cp.max(drawdowns).item()
        max_drawdown_pct = (max_drawdown / INITIAL_CAPITAL) * 100 if INITIAL_CAPITAL > 0 else 0

        # Sharpe ratio (simplified, GPU accelerated)
        if len(pnl_pct_values) > 1:
            returns_std = cp.std(pnl_pct_values).item()
            avg_return = cp.mean(pnl_pct_values).item()
            sharpe_ratio = (avg_return / returns_std) if returns_std > 0 else 0
        else:
            sharpe_ratio = 0

        # Average holding period
        avg_holding_period = cp.mean(holding_periods).item()

        # ROI calculation
        roi = total_pnl_pct
        is_profitable = roi >= PROFIT_THRESHOLD

        return {
            'stock_name': symbol,
            'strategy_name': strategy_name,
            'timeframe': timeframe,
            'risk_reward_ratio': f"{rr_combo[0]}:{rr_combo[1]}",
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'accuracy': round(accuracy * 100, 2),
            'total_pnl': round(total_pnl, 2),
            'roi': round(roi, 2),
            'expectancy': round(expectancy, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'profit_factor': round(profit_factor, 2),
            'max_drawdown': round(max_drawdown, 2),
            'max_drawdown_pct': round(max_drawdown_pct, 2),
            'sharpe_ratio': round(sharpe_ratio, 2),
            'avg_holding_period': round(avg_holding_period, 1),
            'is_profitable': is_profitable
        }

    except Exception as e:
        logger.warning(f"GPU performance calculation failed: {e}, falling back to CPU")
        return calculate_performance_metrics(trades, symbol, strategy_name, timeframe, rr_combo)

def find_exit_vectorized_gpu(df: pl.DataFrame, entry_idx: int, signal_type: int,
                           profit_target: float, stop_loss: float, timeframe: str) -> Optional[Tuple[float, int, str]]:
    """GPU-accelerated exit finding using CuPy for numerical operations"""
    try:
        if not GPU_AVAILABLE or not USE_GPU_ACCELERATION:
            return find_exit_vectorized_polars(df, entry_idx, signal_type, profit_target, stop_loss, timeframe)

        # Get data after entry point
        future_data = df.slice(entry_idx + 1, min(100, len(df) - entry_idx - 1))

        if len(future_data) == 0:
            return None

        # Convert to CuPy arrays for GPU processing
        highs = cp.array(future_data['high'].to_list())
        lows = cp.array(future_data['low'].to_list())
        closes = cp.array(future_data['close'].to_list())

        # GPU-accelerated exit condition calculations
        if signal_type == 1:  # Long position
            profit_hits = highs >= profit_target
            stop_hits = lows <= stop_loss
        else:  # Short position
            profit_hits = lows <= profit_target
            stop_hits = highs >= stop_loss

        # Find first exit using GPU operations
        profit_indices = cp.where(profit_hits)[0]
        stop_indices = cp.where(stop_hits)[0]

        # Determine which exit comes first
        first_profit = profit_indices[0].item() if len(profit_indices) > 0 else float('inf')
        first_stop = stop_indices[0].item() if len(stop_indices) > 0 else float('inf')

        # Default to end-of-day exit if no other exit found
        max_holding = min(20, len(future_data) - 1)

        if first_profit < first_stop and first_profit < max_holding:
            # Profit target hit first
            exit_price = profit_target
            holding_period = first_profit + 1
            exit_reason = "profit"
        elif first_stop < first_profit and first_stop < max_holding:
            # Stop loss hit first
            exit_price = stop_loss
            holding_period = first_stop + 1
            exit_reason = "stop"
        else:
            # End of day exit
            holding_period = max_holding
            exit_price = closes[max_holding - 1].item()
            exit_reason = "eod"

        return exit_price, holding_period, exit_reason

    except Exception as e:
        logger.debug(f"GPU exit finding failed: {e}, falling back to CPU")
        return find_exit_vectorized_polars(df, entry_idx, signal_type, profit_target, stop_loss, timeframe)

def process_symbol_multiprocessing(args):
    """Multiprocessing wrapper for symbol processing - runs in separate process"""
    symbol_data_dict, strategies, timeframe, symbol = args

    try:
        # Reconstruct DataFrame from dict (needed for multiprocessing)
        symbol_df = pl.DataFrame(symbol_data_dict)

        if len(symbol_df) < 10:
            return []

        # Process strategies for this symbol
        all_results = []
        for strategy in strategies:
            for rr in RISK_REWARD_RATIOS:
                # Use synchronous version for multiprocessing
                result = backtest_strategy_rr_sync(symbol_df, strategy, timeframe, symbol, rr)
                if result:
                    all_results.append(result)

        return all_results

    except Exception as e:
        logger.error(f"Multiprocessing symbol {symbol} failed: {e}")
        return []

def backtest_strategy_rr_sync(symbol_df: pl.DataFrame, strategy: Dict[str, Any],
                             timeframe: str, symbol: str, rr: List[float]) -> Optional[Dict[str, Any]]:
    """Synchronous version for multiprocessing with GPU acceleration"""
    try:
        # Use the existing simulate_trades_vectorized logic but synchronously
        trades = simulate_trades_sync(symbol_df, strategy, rr, timeframe)

        if not trades:
            return None

        # Use GPU-accelerated performance calculation if available
        if GPU_AVAILABLE:
            result = calculate_performance_metrics_gpu(trades, symbol, strategy['name'], timeframe, rr)
        else:
            result = calculate_performance_metrics(trades, symbol, strategy['name'], timeframe, rr)

        return result

    except Exception as e:
        logger.error(f"Sync backtest strategy R:R failed: {e}")
        return None

def simulate_trades_sync(df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float],
                        timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Synchronous version of trade simulation for multiprocessing"""
    try:
        # Sort by datetime and remove nulls
        df = df.sort("datetime").drop_nulls(subset=['close', 'high', 'low', 'open', 'volume', 'datetime'])

        if len(df) < 20:
            return None

        # Generate signals
        long_signals = generate_strategy_signals(df, "long", strategy)
        short_signals = generate_strategy_signals(df, "short", strategy)

        # Create signal dataframe
        df_signals = df.with_columns([
            pl.when(long_signals).then(1).when(short_signals).then(-1).otherwise(0).alias("signal"),
            pl.int_range(pl.len()).alias("row_idx"),
        ])

        # Filter for actual signals only
        signals_only = df_signals.filter(pl.col("signal").is_in([1, -1]))

        if len(signals_only) == 0:
            return None

        # Process signals synchronously
        return process_signals_sync(df_signals, signals_only, strategy, rr, timeframe)

    except Exception as e:
        logger.error(f"Sync trade simulation failed: {e}")
        return None

def process_signals_sync(df_all: pl.DataFrame, signals_df: pl.DataFrame,
                        strategy: Dict[str, Any], rr: List[float], timeframe: str) -> List[Dict[str, Any]]:
    """Synchronous signal processing for multiprocessing"""
    trades = []
    capital = strategy.get('capital', INITIAL_CAPITAL)

    # Limit number of trades for performance
    max_trades = min(20, len(signals_df))

    for i, signal_row in enumerate(signals_df.head(max_trades).iter_rows(named=True)):
        entry_idx = signal_row['row_idx']
        signal_type = signal_row['signal']
        entry_price = signal_row['close']
        entry_time = signal_row['datetime']

        # Calculate position size
        if signal_type == 1:  # Long
            stop_loss_price = entry_price * (1 - rr[0] / 100)
            profit_target_price = entry_price * (1 + rr[1] / 100)
        else:  # Short
            stop_loss_price = entry_price * (1 + rr[0] / 100)
            profit_target_price = entry_price * (1 - rr[1] / 100)

        position_value, quantity = calculate_intraday_position_size(
            capital, entry_price, stop_loss_price, signal_type
        )

        if quantity <= 0:
            continue

        # Find exit using GPU acceleration if available
        if GPU_AVAILABLE and USE_GPU_ACCELERATION:
            exit_data = find_exit_vectorized_gpu(
                df_all, entry_idx, signal_type, profit_target_price, stop_loss_price, timeframe
            )
        else:
            exit_data = find_exit_vectorized_polars(
                df_all, entry_idx, signal_type, profit_target_price, stop_loss_price, timeframe
            )

        if exit_data is None:
            continue

        exit_price, holding_period, exit_reason = exit_data

        # Calculate PnL
        trade_pnl, pnl_pct = calculate_trade_pnl_fast(
            signal_type, entry_price, exit_price, quantity, position_value
        )

        trade = {
            'entry_time': entry_time,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'signal': signal_type,
            'pnl': trade_pnl,
            'pnl_pct': pnl_pct,
            'position_size': position_value,
            'quantity': quantity,
            'holding_period': holding_period,
            'exit_reason': exit_reason
        }
        trades.append(trade)

    return trades

def load_strategies() -> List[Dict[str, Any]]:
    """Load ALL strategies from YAML file"""
    try:
        with open(STRATEGIES_FILE, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        strategies = data.get('strategies', [])

        if MAX_STRATEGIES:
            strategies = strategies[:MAX_STRATEGIES]
            logger.info(f"[LIST] Loaded {len(strategies)} strategies (limited for testing)")
        else:
            logger.info(f"[LIST] Loaded ALL {len(strategies)} strategies")

        return strategies
    except Exception as e:
        logger.error(f"Failed to load strategies from {STRATEGIES_FILE}: {e}")
        return []

def main():
    """Main synchronous entry point"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        logger.info("⏹️ Interrupted by user")
        cleanup_process_pool()
    except Exception as e:
        logger.error(f"[ERROR] Fatal error: {e}")
        cleanup_process_pool()
        raise

if __name__ == "__main__":
    main()

def evaluate_strategy_condition(df: pl.DataFrame, condition_str: str) -> pl.Series:
    """Evaluate a strategy condition string using polars operations"""
    try:
        # Split the condition by & and | operators
        import re

        # Replace & with logical and, | with logical or
        # First, handle parentheses and complex expressions by evaluating sub-conditions

        # For now, let's handle simple conditions separated by &
        conditions = condition_str.split(' & ')

        results = []
        for condition in conditions:
            condition = condition.strip()
            result = evaluate_single_condition(df, condition)
            results.append(result)

        # Combine all conditions with logical AND
        if results:
            combined = results[0]
            for result in results[1:]:
                combined = combined & result
            return combined.fill_null(False)
        else:
            return pl.Series([False] * len(df))

    except Exception as e:
        logger.debug(f"Error in condition evaluation: {e}")
        return pl.Series([False] * len(df))

def evaluate_single_condition(df: pl.DataFrame, condition_str: str) -> pl.Series:
    """Evaluate a single condition (no & or | operators)"""
    try:
        import re

        # Create evaluation context
        eval_context = {}
        for col in df.columns:
            eval_context[col] = df[col]

        # Handle rolling operations
        rolling_pattern = r'(\w+)\.rolling\((\d+)\)\.mean\(\)'
        rolling_matches = re.findall(rolling_pattern, condition_str)

        for col_name, window in rolling_matches:
            if col_name in df.columns:
                rolling_col_name = f"{col_name}_rolling_{window}_mean"
                eval_context[rolling_col_name] = df[col_name].rolling_mean(int(window), min_samples=1)
                old_pattern = f"{col_name}.rolling({window}).mean()"
                condition_str = condition_str.replace(old_pattern, rolling_col_name)

        # Handle shift operations
        shift_pattern = r'(\w+)\.shift\((\d+)\)'
        shift_matches = re.findall(shift_pattern, condition_str)

        for col_name, shift_val in shift_matches:
            if col_name in df.columns:
                shift_col_name = f"{col_name}_shift_{shift_val}"
                eval_context[shift_col_name] = df[col_name].shift(int(shift_val))
                old_pattern = f"{col_name}.shift({shift_val})"
                condition_str = condition_str.replace(old_pattern, shift_col_name)

        # Handle rolling max/min operations
        rolling_max_pattern = r'(\w+)\.rolling\((\d+)\)\.max\(\)'
        rolling_max_matches = re.findall(rolling_max_pattern, condition_str)

        for col_name, window in rolling_max_matches:
            if col_name in df.columns:
                rolling_col_name = f"{col_name}_rolling_{window}_max"
                eval_context[rolling_col_name] = df[col_name].rolling_max(int(window), min_samples=1)
                old_pattern = f"{col_name}.rolling({window}).max()"
                condition_str = condition_str.replace(old_pattern, rolling_col_name)

        rolling_min_pattern = r'(\w+)\.rolling\((\d+)\)\.min\(\)'
        rolling_min_matches = re.findall(rolling_min_pattern, condition_str)

        for col_name, window in rolling_min_matches:
            if col_name in df.columns:
                rolling_col_name = f"{col_name}_rolling_{window}_min"
                eval_context[rolling_col_name] = df[col_name].rolling_min(int(window), min_samples=1)
                old_pattern = f"{col_name}.rolling({window}).min()"
                condition_str = condition_str.replace(old_pattern, rolling_col_name)

        # Evaluate the single condition
        try:
            result = eval(condition_str, {"__builtins__": {}}, eval_context)
            if isinstance(result, pl.Series):
                return result.fill_null(False)
            else:
                # If result is a scalar, create a series
                return pl.Series([bool(result)] * len(df))
        except Exception as e:
            logger.debug(f"Failed to evaluate single condition '{condition_str}': {e}")
            return pl.Series([False] * len(df))

    except Exception as e:
        logger.debug(f"Error in single condition evaluation: {e}")
        return pl.Series([False] * len(df))

def generate_strategy_signals(df: pl.DataFrame, strategy_type: str, strategy: Dict[str, Any]) -> pl.Series:
    """Generate strategy signals using actual strategy definitions"""
    try:
        # Get the strategy condition for the specified type
        condition_key = strategy_type  # 'long' or 'short'
        condition_str = strategy.get(condition_key, "")

        if not condition_str:
            logger.debug(f"No {strategy_type} condition found for strategy {strategy.get('name', 'unknown')}")
            return pl.Series([False] * len(df))

        # Evaluate the condition
        signals = evaluate_strategy_condition(df, condition_str)

        # Apply minimum gap between signals to avoid over-trading
        min_gap = 5  # Minimum 5 bars between signals
        if signals.sum() > 0:
            # Use polars operations to find signal indices
            signal_indices = [i for i, val in enumerate(signals.to_list()) if val]
            filtered_indices = []
            last_signal = -min_gap

            for idx in signal_indices:
                if idx - last_signal >= min_gap:
                    filtered_indices.append(idx)
                    last_signal = idx

            # Create filtered signals using polars
            filtered_signals = pl.Series([False] * len(df))
            if filtered_indices:
                for idx in filtered_indices:
                    if idx < len(filtered_signals):
                        filtered_signals = filtered_signals.scatter(idx, True)

            return filtered_signals.fill_null(False)

        return signals.fill_null(False)

    except Exception as e:
        logger.warning(f"Signal generation failed for strategy {strategy.get('name', 'unknown')}: {e}")
        return pl.Series([False] * len(df))

    finally:
        # Clear local variables to prevent accumulation
        try:
            del signals
        except:
            pass

def calculate_intraday_position_size(capital: float, entry_price: float, stop_loss_price: float,
                                   signal_type: int) -> Tuple[float, int]:
    """
    Calculate position size for intraday trading with proper risk management

    Args:
        capital: Available capital
        entry_price: Entry price of the trade
        stop_loss_price: Stop loss price
        signal_type: 1 for long, -1 for short

    Returns:
        Tuple of (position_value, quantity)
    """
    try:
        # Calculate risk per share
        if signal_type == 1:  # Long trade
            risk_per_share = abs(entry_price - stop_loss_price)
        else:  # Short trade
            risk_per_share = abs(stop_loss_price - entry_price)

        if risk_per_share <= 0:
            return 0, 0

        # Calculate quantity based on 1% risk
        risk_amount = capital * (RISK_PER_TRADE_PCT / 100)
        quantity = int(risk_amount / risk_per_share)

        if quantity <= 0:
            return 0, 0

        # Calculate position value
        position_value = quantity * entry_price

        # Check intraday margin limit (3.5x capital)
        max_position_value = capital * INTRADAY_MARGIN_MULTIPLIER

        if position_value > max_position_value:
            # Reduce quantity to fit within margin limits
            quantity = int(max_position_value / entry_price)
            position_value = quantity * entry_price

        return position_value, quantity

    except Exception as e:
        logger.warning(f"Position sizing calculation failed: {e}")
        return 0, 0

def get_intraday_exit_time(entry_time, timeframe: str):
    """
    Calculate the latest exit time for intraday trading (3:20 PM IST)
    Ensures no carry-forward to next day
    """
    try:
        # Extract date from entry time
        entry_date = entry_time.date() if hasattr(entry_time, 'date') else entry_time

        # Set exit time to 3:20 PM (15:20) on the same day
        from datetime import datetime, time
        exit_time = datetime.combine(entry_date, time(15, 20))

        return exit_time

    except Exception:
        # Fallback: return entry time + reasonable holding period
        return entry_time

async def simulate_trades_vectorized(df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float],
                                    timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Ultra-fast vectorized trade simulation using pure polars operations"""
    try:
        # Sort by datetime and remove nulls
        df = df.sort("datetime").drop_nulls(subset=['close', 'high', 'low', 'open', 'volume', 'datetime'])

        if len(df) < 20:
            return None

        # Generate signals using polars (vectorized)
        long_signals = generate_strategy_signals(df, "long", strategy)
        short_signals = generate_strategy_signals(df, "short", strategy)

        # Create comprehensive signal dataframe with all needed columns
        df_signals = df.with_columns([
            pl.when(long_signals).then(1).when(short_signals).then(-1).otherwise(0).alias("signal"),
            pl.int_range(pl.len()).alias("row_idx"),
            # Pre-calculate profit targets and stop losses for vectorization
            (pl.col("close") * (1 + rr[1] / 100)).alias("long_profit_target"),
            (pl.col("close") * (1 - rr[0] / 100)).alias("long_stop_loss"),
            (pl.col("close") * (1 - rr[1] / 100)).alias("short_profit_target"),
            (pl.col("close") * (1 + rr[0] / 100)).alias("short_stop_loss"),
        ])

        # Filter for actual signals only
        signals_only = df_signals.filter(pl.col("signal").is_in([1, -1]))

        if len(signals_only) == 0:
            return None

        # Use vectorized approach instead of row iteration
        return await process_signals_vectorized(df_signals, signals_only, strategy, rr, timeframe)

    except Exception as e:
        logger.error(f"Vectorized trade simulation failed: {e}")
        return None

async def process_signals_vectorized(df_all: pl.DataFrame, signals_df: pl.DataFrame,
                                   strategy: Dict[str, Any], rr: List[float], timeframe: str) -> List[Dict[str, Any]]:
    """Ultra-fast vectorized signal processing using polars operations - 10x faster than loops"""
    try:
        if len(signals_df) == 0:
            return []

        capital = strategy.get('capital', INITIAL_CAPITAL)

        # Use polars for vectorized operations instead of converting to PyArrow
        # This is much faster for this type of processing

        # Create a comprehensive signals dataframe with all exit conditions pre-calculated
        signals_with_exits = signals_df.with_columns([
            # Calculate position size based on risk
            (capital * (RISK_PER_TRADE_PCT / 100) /
             pl.when(pl.col("signal") == 1)
             .then((pl.col("close") - pl.col("long_stop_loss")) / pl.col("close"))
             .otherwise((pl.col("short_stop_loss") - pl.col("close")) / pl.col("close"))
            ).alias("position_size"),

            # Calculate quantity
            (capital * (RISK_PER_TRADE_PCT / 100) /
             pl.when(pl.col("signal") == 1)
             .then(pl.col("close") - pl.col("long_stop_loss"))
             .otherwise(pl.col("short_stop_loss") - pl.col("close"))
            ).alias("quantity")
        ])

        # Vectorized exit finding using polars operations - much faster than loops
        trades = []

        # Process all signals at once using vectorized operations
        for signal_row in signals_with_exits.iter_rows(named=True):
            entry_idx = signal_row['row_idx']
            signal_type = signal_row['signal']
            entry_price = signal_row['close']
            entry_time = signal_row['datetime']
            position_size = signal_row['position_size']
            quantity = signal_row['quantity']

            if quantity <= 0:
                continue

            # Get profit target and stop loss
            if signal_type == 1:  # Long
                profit_target = signal_row['long_profit_target']
                stop_loss = signal_row['long_stop_loss']
            else:  # Short
                profit_target = signal_row['short_profit_target']
                stop_loss = signal_row['short_stop_loss']

            # Find exit using vectorized polars operations
            exit_data = find_exit_vectorized_polars(
                df_all, entry_idx, signal_type, profit_target, stop_loss, timeframe
            )

            if exit_data is None:
                continue

            exit_price, holding_period, exit_reason = exit_data

            # Calculate PnL
            trade_pnl, pnl_pct = calculate_trade_pnl_fast(
                signal_type, entry_price, exit_price, quantity, position_size
            )

            trade = {
                'entry_time': entry_time,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'signal': signal_type,
                'pnl': trade_pnl,
                'pnl_pct': pnl_pct,
                'position_size': position_size,
                'quantity': quantity,
                'holding_period': holding_period,
                'exit_reason': exit_reason
            }
            trades.append(trade)

        return trades

    except Exception as e:
        logger.error(f"Vectorized signal processing failed: {e}")
        return []

def find_exit_vectorized(entry_idx: int, signal_type: int, profit_target: float, stop_loss: float,
                        highs: pa.Array, lows: pa.Array, closes: pa.Array,
                        times: list, entry_time, timeframe: str) -> tuple:
    """Ultra-fast vectorized exit detection using PyArrow compute functions"""
    try:
        from datetime import datetime, time

        # Calculate intraday exit deadline (3:20 PM same day)
        entry_date = entry_time.date() if hasattr(entry_time, 'date') else entry_time
        exit_deadline = datetime.combine(entry_date, time(15, 20))

        # Look ahead maximum 20 bars or until end of data
        max_look_ahead = min(20, len(highs) - entry_idx - 1)
        if max_look_ahead <= 0:
            return False, 0, 0, 'no_data'

        # Get future price arrays using PyArrow slicing (vectorized)
        start_idx = entry_idx + 1
        end_idx = start_idx + max_look_ahead
        future_highs = highs[start_idx:end_idx]
        future_lows = lows[start_idx:end_idx]
        future_closes = closes[start_idx:end_idx]
        future_times = times[start_idx:end_idx]

        if signal_type == 1:  # Long trade
            # Vectorized profit target detection using PyArrow compute
            profit_hits = pc.greater_equal(future_highs, profit_target)
            # Vectorized stop loss detection using PyArrow compute
            stop_hits = pc.less_equal(future_lows, stop_loss)
        else:  # Short trade
            # Vectorized profit target detection using PyArrow compute
            profit_hits = pc.less_equal(future_lows, profit_target)
            # Vectorized stop loss detection using PyArrow compute
            stop_hits = pc.greater_equal(future_highs, stop_loss)

        # Check for intraday deadline hits using PyArrow
        deadline_hits = pa.array([t >= exit_deadline for t in future_times])

        # Find first occurrence of any exit condition using PyArrow compute
        try:
            profit_idx = pc.index(profit_hits, pa.scalar(True)).as_py() if pc.any(profit_hits).as_py() else max_look_ahead
        except:
            profit_idx = max_look_ahead

        try:
            stop_idx = pc.index(stop_hits, pa.scalar(True)).as_py() if pc.any(stop_hits).as_py() else max_look_ahead
        except:
            stop_idx = max_look_ahead

        try:
            deadline_idx = pc.index(deadline_hits, pa.scalar(True)).as_py() if pc.any(deadline_hits).as_py() else max_look_ahead
        except:
            deadline_idx = max_look_ahead

        # Determine which exit condition occurs first
        exit_idx = min(profit_idx, stop_idx, deadline_idx)

        if exit_idx >= max_look_ahead:
            # Time-based exit at last available bar using PyArrow
            exit_price = future_closes[-1].as_py()
            holding_period = max_look_ahead
            exit_reason = 'time_exit'
        elif exit_idx == profit_idx and profit_hits[exit_idx].as_py():
            exit_price = profit_target
            holding_period = exit_idx + 1
            exit_reason = 'profit_target'
        elif exit_idx == stop_idx and stop_hits[exit_idx].as_py():
            exit_price = stop_loss
            holding_period = exit_idx + 1
            exit_reason = 'stop_loss'
        else:  # deadline_idx
            exit_price = future_closes[exit_idx].as_py()
            holding_period = exit_idx + 1
            exit_reason = 'intraday_exit'

        return True, exit_price, holding_period, exit_reason

    except Exception as e:
        logger.warning(f"PyArrow vectorized exit detection failed: {e}")
        return False, 0, 0, 'error'

def calculate_trade_pnl(signal_type: int, entry_price: float, exit_price: float,
                       quantity: int, position_value: float) -> tuple:
    """Fast PnL calculation with slippage and transaction costs"""
    try:
        # Apply slippage
        if signal_type == 1:  # Long
            entry_price_adj = entry_price * (1 + SLIPPAGE_PCT / 100)
            exit_price_adj = exit_price * (1 - SLIPPAGE_PCT / 100)
            trade_pnl = quantity * (exit_price_adj - entry_price_adj)
        else:  # Short
            entry_price_adj = entry_price * (1 - SLIPPAGE_PCT / 100)
            exit_price_adj = exit_price * (1 + SLIPPAGE_PCT / 100)
            trade_pnl = quantity * (entry_price_adj - exit_price_adj)

        # Calculate percentage return
        pnl_pct = (trade_pnl / position_value) * 100 if position_value > 0 else 0

        # Apply transaction costs
        transaction_cost = position_value * (TRANSACTION_COST_PCT / 100)
        trade_pnl -= transaction_cost
        pnl_pct -= TRANSACTION_COST_PCT

        return trade_pnl, pnl_pct

    except Exception as e:
        logger.warning(f"PnL calculation failed: {e}")
        return 0, 0

def calculate_performance_metrics(trades: List[Dict[str, Any]], symbol: str, strategy_name: str,
                                timeframe: str, rr_combo: List[float]) -> Optional[Dict[str, Any]]:
    """Calculate comprehensive performance metrics using pyarrow for numerical operations"""
    try:
        if not trades or len(trades) == 0:
            return None

        # Convert trades to pyarrow arrays for efficient computation
        pnl_values = pa.array([t['pnl'] for t in trades])
        pnl_pct_values = pa.array([t['pnl_pct'] for t in trades])
        holding_periods = pa.array([t['holding_period'] for t in trades])

        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if t['pnl'] > 0)
        losing_trades = total_trades - winning_trades

        # Basic calculations using pyarrow compute functions
        total_pnl = pa.compute.sum(pnl_values).as_py()
        total_pnl_pct = pa.compute.sum(pnl_pct_values).as_py()

        # Accuracy
        accuracy = (winning_trades / total_trades) if total_trades > 0 else 0

        # Expectancy
        expectancy = total_pnl / total_trades if total_trades > 0 else 0

        # Win/Loss calculations
        wins = [t['pnl'] for t in trades if t['pnl'] > 0]
        losses = [t['pnl'] for t in trades if t['pnl'] < 0]

        avg_win = pa.compute.mean(pa.array(wins)).as_py() if wins else 0
        avg_loss = pa.compute.mean(pa.array(losses)).as_py() if losses else 0

        # Profit Factor
        sum_wins = sum(wins) if wins else 0
        sum_losses = abs(sum(losses)) if losses else 0
        profit_factor = sum_wins / sum_losses if sum_losses > 0 else (1.0 if sum_wins > 0 else 0)

        # Risk-Reward Ratio
        risk_reward_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0

        # Max drawdown calculation
        running_pnl = 0
        peak = 0
        max_dd = 0
        drawdown_periods = []
        current_dd_duration = 0

        for trade in trades:
            running_pnl += trade['pnl']
            if running_pnl > peak:
                peak = running_pnl
                if current_dd_duration > 0:
                    drawdown_periods.append(current_dd_duration)
                    current_dd_duration = 0
            else:
                current_dd_duration += 1

            drawdown = (peak - running_pnl) / peak if peak > 0 else 0
            max_dd = max(max_dd, drawdown)

        # Drawdown duration
        avg_drawdown_duration = pa.compute.mean(pa.array(drawdown_periods)).as_py() if drawdown_periods else 0

        # Returns for Sharpe ratio
        returns = [t['pnl_pct'] / 100 for t in trades]
        returns_array = pa.array(returns)
        mean_return = pa.compute.mean(returns_array).as_py() if returns else 0
        std_return = pa.compute.stddev(returns_array).as_py() if len(returns) > 1 else 0

        # Sharpe ratio (annualized)
        if std_return > 0.001:
            sharpe_ratio = (mean_return / std_return * (252 ** 0.5))
            sharpe_ratio = max(-5, min(5, sharpe_ratio))  # Cap to prevent overflow
        else:
            sharpe_ratio = 0

        # Volatility
        volatility = std_return * (252 ** 0.5) if std_return > 0 else 0

        # Market regime classification
        if total_pnl_pct > 5:
            market_regime = "bull"
        elif total_pnl_pct < -3:
            market_regime = "bear"
        else:
            market_regime = "sideways"

        # Liquidity score
        liquidity_score = min(total_trades / 50, 1.0)  # Normalized by max 50 trades
        avg_holding = pa.compute.mean(holding_periods).as_py()
        holding_score = max(0, 1 - (avg_holding / 20))
        liquidity_final = (liquidity_score + holding_score) / 2

        if liquidity_final > 0.7:
            liquidity = "High"
        elif liquidity_final > 0.4:
            liquidity = "Medium"
        else:
            liquidity = "Low"

        # Correlation index (simplified)
        correlation_index = 0.0
        if len(returns) > 1:
            try:
                returns_shifted = returns[1:] + [0]  # Simple lag correlation
                corr_array = pa.array(returns[:-1])
                corr_shifted = pa.array(returns_shifted[:-1])
                # Simple correlation approximation
                correlation_index = pa.compute.covariance(corr_array, corr_shifted).as_py() / (
                    pa.compute.stddev(corr_array).as_py() * pa.compute.stddev(corr_shifted).as_py() + 1e-8)
                correlation_index = max(-1, min(1, correlation_index))
            except:
                correlation_index = 0.0

        # Build result dictionary with all required columns
        result = {
            'strategy_name': strategy_name,
            'stock_name': symbol,
            'timeframe': timeframe,
            'n_trades': total_trades,
            'ROI': total_pnl_pct,
            'accuracy': accuracy,
            'expectancy': expectancy,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_dd * 100,
            'profit_factor': profit_factor,
            'avg_holding_period': avg_holding,
            'risk_reward_ratio': risk_reward_ratio,
            'capital_at_risk': RISK_PER_TRADE_PCT,
            'liquidity': liquidity,
            'volatility': volatility,
            'market_regime': market_regime,
            'correlation_index': correlation_index,
            'drawdown_duration': avg_drawdown_duration,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'total_pnl': total_pnl,
            'position_size_pct': RISK_PER_TRADE_PCT,
            'risk_reward_combo': f"{rr_combo[0]}_{rr_combo[1]}",
            'is_profitable': total_pnl_pct > PROFIT_THRESHOLD
        }

        return result

    except Exception as e:
        logger.error(f"Metrics calculation failed: {e}")
        return None

async def backtest_strategy_rr_async(symbol_df: pl.DataFrame, strategy: Dict[str, Any],
                                   timeframe: str, symbol: str, rr: List[float]) -> Optional[Dict[str, Any]]:
    """Async backtest for single strategy with specific R:R ratio"""
    try:
        trades = await simulate_trades_vectorized(symbol_df, strategy, rr, timeframe)

        if not trades:
            return None

        result = calculate_performance_metrics(trades, symbol, strategy['name'], timeframe, rr)
        return result

    except Exception as e:
        logger.error(f"Backtest strategy R:R failed: {e}")
        return None

async def process_strategy_async(symbol_df: pl.DataFrame, strategy: Dict[str, Any],
                               timeframe: str, symbol: str) -> List[Dict[str, Any]]:
    """Process single strategy with all R:R ratios asynchronously"""
    strategy_results = []

    # Create tasks for all R:R ratios
    tasks = []
    for rr in RISK_REWARD_RATIOS:
        task = backtest_strategy_rr_async(symbol_df, strategy, timeframe, symbol, rr)
        tasks.append(task)

    # Execute all R:R ratios concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)

    for result in results:
        if isinstance(result, Exception):
            logger.error(f"R:R task failed: {result}")
            continue
        if result:
            strategy_results.append(result)

    return strategy_results

async def process_symbol_file_async(file_path: str, symbol: str, timeframe: str, 
                                  strategies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process single symbol file with all strategies"""
    logger.info(f"[INIT] Processing {symbol} ({timeframe}) from {file_path}")
    
    try:
        # Load the symbol data
        symbol_df = pl.read_parquet(file_path)
        
        if len(symbol_df) < 10:
            logger.warning(f"[WARN] Insufficient data for {symbol}: {len(symbol_df)} rows")
            return []
        
        logger.info(f"[DATA] Loaded {len(symbol_df):,} rows for {symbol}")
        
        # Process strategies in batches to manage memory
        strategy_batches = [strategies[i:i+CONCURRENT_STRATEGIES] 
                          for i in range(0, len(strategies), CONCURRENT_STRATEGIES)]
        all_results = []
        
        for batch_idx, strategy_batch in enumerate(strategy_batches):
            logger.info(f"  [STATUS] Strategy batch {batch_idx+1}/{len(strategy_batches)} ({len(strategy_batch)} strategies)")
            
            # Create tasks for current batch
            tasks = []
            for strategy in strategy_batch:
                task = process_strategy_async(symbol_df, strategy, timeframe, symbol)
                tasks.append(task)
            
            # Execute batch concurrently
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect results
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Strategy batch failed: {result}")
                    continue
                if result:
                    all_results.extend(result)
            
            # Memory cleanup after each strategy batch
            del tasks, batch_results
            aggressive_memory_cleanup()
        
        logger.info(f"[SUCCESS] {symbol}: {len(all_results)} total results")
        return all_results
        
    except Exception as e:
        logger.error(f"[ERROR] Failed to process {symbol}: {e}")
        return []

async def write_symbol_results_async(results: List[Dict[str, Any]], symbol: str, timeframe: str):
    """Write results for a single symbol to its dedicated output file"""
    if not results:
        return
    
    try:
        # Generate output filename
        output_filename = generate_output_filename(symbol, timeframe)
        output_path = os.path.join(OUTPUT_DIR, output_filename)
        
        with _file_write_lock:
            # Convert to polars DataFrame
            df_out = pl.DataFrame(results)
            
            # Create output directory
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write to parquet with compression
            df_out.write_parquet(output_path, compression=COMPRESSION)
            
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            logger.info(f"[SUCCESS] Written {len(results)} results for {symbol} to {output_filename} ({file_size:.1f}MB)")
            
            # Clear DataFrame reference
            del df_out
            
    except Exception as e:
        logger.error(f"[ERROR] Error writing results for {symbol}: {e}")
    finally:
        # Force cleanup
        gc.collect()

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 1] MULTI-STRATEGY & MULTI-TIMEFRAME BACKTESTING
# ═══════════════════════════════════════════════════════════════════════════════

class MultiStrategyBacktester:
    """Advanced multi-strategy and multi-timeframe backtesting engine"""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.strategies = []
        self.timeframes = []
        self.symbols = []
        self.results = {}
        self.portfolio_metrics = {}
        self.correlation_matrix = None

    async def add_strategy(self, strategy: Dict[str, Any], metadata: StrategyMetadata):
        """Add strategy to the backtesting portfolio"""
        strategy_id = f"{metadata.name}_{uuid.uuid4().hex[:8]}"
        self.strategies.append({
            'id': strategy_id,
            'strategy': strategy,
            'metadata': metadata,
            'allocation': 1.0 / len(self.strategies) if self.strategies else 1.0
        })
        logger.info(f"[MULTI] Added strategy: {metadata.name} (ID: {strategy_id})")

    async def run_multi_strategy_backtest(self, data_sources: Dict[str, Dict[str, pl.DataFrame]]) -> Dict[str, Any]:
        """Run comprehensive multi-strategy, multi-timeframe backtest"""
        logger.info("[MULTI] Starting multi-strategy backtesting...")

        # Initialize results storage
        strategy_results = {}
        portfolio_equity_curves = {}

        # Run each strategy on each timeframe
        for strategy_info in self.strategies:
            strategy_id = strategy_info['id']
            strategy = strategy_info['strategy']
            metadata = strategy_info['metadata']

            logger.info(f"[MULTI] Processing strategy: {metadata.name}")

            # Get applicable timeframes for this strategy
            applicable_timeframes = metadata.timeframes or list(data_sources.keys())

            strategy_results[strategy_id] = {}

            for timeframe in applicable_timeframes:
                if timeframe not in data_sources:
                    continue

                logger.info(f"[MULTI] Running {metadata.name} on {timeframe}")

                # Run strategy on all symbols for this timeframe
                timeframe_results = await self._run_strategy_on_timeframe(
                    strategy, metadata, timeframe, data_sources[timeframe]
                )

                strategy_results[strategy_id][timeframe] = timeframe_results

        # Calculate portfolio-level metrics
        portfolio_metrics = await self._calculate_portfolio_metrics(strategy_results)

        # Analyze strategy correlations
        correlation_analysis = await self._analyze_strategy_correlations(strategy_results)

        # Generate comprehensive report
        final_results = {
            'strategy_results': strategy_results,
            'portfolio_metrics': portfolio_metrics,
            'correlation_analysis': correlation_analysis,
            'config': asdict(self.config),
            'execution_timestamp': datetime.now(),
            'total_strategies': len(self.strategies),
            'total_timeframes': len(data_sources)
        }

        logger.info(f"[MULTI] Completed multi-strategy backtest: {len(self.strategies)} strategies, {len(data_sources)} timeframes")
        return final_results

    async def _run_strategy_on_timeframe(self, strategy: Dict[str, Any], metadata: StrategyMetadata,
                                       timeframe: str, symbol_data: Dict[str, pl.DataFrame]) -> Dict[str, Any]:
        """Run single strategy on all symbols for a specific timeframe"""
        timeframe_results = {}

        for symbol, df in symbol_data.items():
            try:
                # Run backtest for this symbol
                trades = await simulate_trades_vectorized(df, strategy, RISK_REWARD_RATIOS, timeframe)

                if trades:
                    # Calculate enhanced metrics
                    metrics = await self._calculate_enhanced_metrics(trades, symbol, metadata.name, timeframe, df)
                    timeframe_results[symbol] = {
                        'trades': trades,
                        'metrics': metrics,
                        'equity_curve': self._generate_equity_curve(trades, df)
                    }

            except Exception as e:
                logger.error(f"[MULTI] Error processing {symbol} on {timeframe}: {e}")
                continue

        return timeframe_results

    async def _calculate_enhanced_metrics(self, trades: List[Dict[str, Any]], symbol: str,
                                        strategy_name: str, timeframe: str, df: pl.DataFrame) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        if not trades:
            return PerformanceMetrics()

        # Basic calculations
        pnl_values = [t['pnl'] for t in trades]
        returns = [t['pnl_pct'] / 100 for t in trades]

        # Calculate metrics
        total_return = sum(returns)
        volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0
        sharpe_ratio = (np.mean(returns) * 252) / volatility if volatility > 0 else 0

        # Drawdown analysis
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = running_max - cumulative_returns
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0

        # Trade statistics
        winning_trades = sum(1 for r in returns if r > 0)
        total_trades = len(trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # Advanced metrics
        downside_returns = [r for r in returns if r < 0]
        downside_volatility = np.std(downside_returns) * np.sqrt(252) if downside_returns else 0
        sortino_ratio = (np.mean(returns) * 252) / downside_volatility if downside_volatility > 0 else 0

        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=total_return * 252 / len(df) if len(df) > 0 else 0,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            total_trades=total_trades,
            win_rate=win_rate,
            profit_factor=sum(r for r in returns if r > 0) / abs(sum(r for r in returns if r < 0)) if any(r < 0 for r in returns) else float('inf')
        )

    def _generate_equity_curve(self, trades: List[Dict[str, Any]], df: pl.DataFrame) -> pl.DataFrame:
        """Generate equity curve for visualization"""
        if not trades:
            return pl.DataFrame()

        # Create equity curve
        equity_data = []
        cumulative_pnl = 0

        for trade in trades:
            cumulative_pnl += trade['pnl']
            equity_data.append({
                'datetime': trade.get('exit_time', datetime.now()),
                'equity': self.config.initial_capital + cumulative_pnl,
                'pnl': trade['pnl'],
                'cumulative_pnl': cumulative_pnl
            })

        return pl.DataFrame(equity_data)

    async def _calculate_portfolio_metrics(self, strategy_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate portfolio-level performance metrics"""
        logger.info("[MULTI] Calculating portfolio metrics...")

        # Aggregate all trades across strategies
        all_trades = []
        strategy_returns = {}

        for strategy_id, timeframe_results in strategy_results.items():
            strategy_trades = []
            for timeframe, symbol_results in timeframe_results.items():
                for symbol, results in symbol_results.items():
                    strategy_trades.extend(results['trades'])

            all_trades.extend(strategy_trades)

            # Calculate strategy-level returns
            if strategy_trades:
                strategy_returns[strategy_id] = [t['pnl_pct'] / 100 for t in strategy_trades]

        # Portfolio-level calculations
        if all_trades:
            portfolio_returns = [t['pnl_pct'] / 100 for t in all_trades]
            portfolio_metrics = {
                'total_trades': len(all_trades),
                'total_return': sum(portfolio_returns),
                'volatility': np.std(portfolio_returns) * np.sqrt(252) if len(portfolio_returns) > 1 else 0,
                'sharpe_ratio': (np.mean(portfolio_returns) * 252) / (np.std(portfolio_returns) * np.sqrt(252)) if len(portfolio_returns) > 1 and np.std(portfolio_returns) > 0 else 0,
                'max_drawdown': self._calculate_max_drawdown(portfolio_returns),
                'win_rate': sum(1 for r in portfolio_returns if r > 0) / len(portfolio_returns) if portfolio_returns else 0
            }
        else:
            portfolio_metrics = {
                'total_trades': 0,
                'total_return': 0,
                'volatility': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'win_rate': 0
            }

        return portfolio_metrics

    async def _analyze_strategy_correlations(self, strategy_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze correlations between strategies"""
        logger.info("[MULTI] Analyzing strategy correlations...")

        # Extract returns for each strategy
        strategy_returns = {}

        for strategy_id, timeframe_results in strategy_results.items():
            all_returns = []
            for timeframe, symbol_results in timeframe_results.items():
                for symbol, results in symbol_results.items():
                    returns = [t['pnl_pct'] / 100 for t in results['trades']]
                    all_returns.extend(returns)

            if all_returns:
                strategy_returns[strategy_id] = all_returns

        # Calculate correlation matrix
        correlation_matrix = {}
        strategy_ids = list(strategy_returns.keys())

        for i, strategy1 in enumerate(strategy_ids):
            correlation_matrix[strategy1] = {}
            for j, strategy2 in enumerate(strategy_ids):
                if i == j:
                    correlation_matrix[strategy1][strategy2] = 1.0
                elif len(strategy_returns[strategy1]) > 1 and len(strategy_returns[strategy2]) > 1:
                    # Calculate correlation
                    min_length = min(len(strategy_returns[strategy1]), len(strategy_returns[strategy2]))
                    corr = np.corrcoef(
                        strategy_returns[strategy1][:min_length],
                        strategy_returns[strategy2][:min_length]
                    )[0, 1]
                    correlation_matrix[strategy1][strategy2] = corr if not np.isnan(corr) else 0.0
                else:
                    correlation_matrix[strategy1][strategy2] = 0.0

        return {
            'correlation_matrix': correlation_matrix,
            'strategy_returns': strategy_returns,
            'diversification_ratio': self._calculate_diversification_ratio(correlation_matrix)
        }

    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """Calculate maximum drawdown from returns"""
        if not returns:
            return 0.0

        cumulative = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdowns = running_max - cumulative
        return np.max(drawdowns) if len(drawdowns) > 0 else 0.0

    def _calculate_diversification_ratio(self, correlation_matrix: Dict[str, Dict[str, float]]) -> float:
        """Calculate portfolio diversification ratio"""
        if not correlation_matrix:
            return 1.0

        n_strategies = len(correlation_matrix)
        if n_strategies <= 1:
            return 1.0

        # Calculate average correlation
        total_correlation = 0
        count = 0

        for strategy1, correlations in correlation_matrix.items():
            for strategy2, corr in correlations.items():
                if strategy1 != strategy2:
                    total_correlation += abs(corr)
                    count += 1

        avg_correlation = total_correlation / count if count > 0 else 0

        # Diversification ratio = 1 / sqrt(1 + (n-1) * avg_correlation)
        diversification_ratio = 1 / np.sqrt(1 + (n_strategies - 1) * avg_correlation)
        return diversification_ratio

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 2] SMART BACKTESTING MODES
# ═══════════════════════════════════════════════════════════════════════════════

class SmartBacktestEngine:
    """Advanced backtesting engine with multiple execution modes"""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.mode = config.mode
        self.results_cache = {}

    async def run_smart_backtest(self, strategy: Dict[str, Any], data: pl.DataFrame,
                               symbol: str, timeframe: str) -> Dict[str, Any]:
        """Run backtest using the specified smart mode"""
        logger.info(f"[SMART] Running {self.mode.value} backtest for {symbol}")

        if self.mode == BacktestMode.DETERMINISTIC:
            return await self._run_deterministic_backtest(strategy, data, symbol, timeframe)
        elif self.mode == BacktestMode.PROBABILISTIC:
            return await self._run_probabilistic_backtest(strategy, data, symbol, timeframe)
        elif self.mode == BacktestMode.ADAPTIVE_AI:
            return await self._run_adaptive_ai_backtest(strategy, data, symbol, timeframe)
        elif self.mode == BacktestMode.MONTE_CARLO:
            return await self._run_monte_carlo_backtest(strategy, data, symbol, timeframe)
        elif self.mode == BacktestMode.WALK_FORWARD:
            return await self._run_walk_forward_backtest(strategy, data, symbol, timeframe)
        else:
            return await self._run_deterministic_backtest(strategy, data, symbol, timeframe)

    async def _run_deterministic_backtest(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                        symbol: str, timeframe: str) -> Dict[str, Any]:
        """Standard deterministic backtesting"""
        trades = await simulate_trades_vectorized(data, strategy, RISK_REWARD_RATIOS, timeframe)

        if not trades:
            return {'mode': 'deterministic', 'trades': [], 'metrics': {}}

        metrics = calculate_performance_metrics(trades, symbol, strategy['name'], timeframe, RISK_REWARD_RATIOS[0])

        return {
            'mode': 'deterministic',
            'trades': trades,
            'metrics': metrics,
            'confidence_interval': None
        }

    async def _run_probabilistic_backtest(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                        symbol: str, timeframe: str) -> Dict[str, Any]:
        """Probabilistic backtesting with uncertainty modeling"""
        logger.info(f"[PROB] Running probabilistic backtest for {symbol}")

        # Add noise to prices to model uncertainty
        results = []
        n_simulations = 100

        for i in range(n_simulations):
            # Add random noise to simulate market uncertainty
            noise_factor = 0.001  # 0.1% noise
            noisy_data = data.with_columns([
                (pl.col("close") * (1 + pl.lit(random.gauss(0, noise_factor)))).alias("close"),
                (pl.col("high") * (1 + pl.lit(random.gauss(0, noise_factor)))).alias("high"),
                (pl.col("low") * (1 + pl.lit(random.gauss(0, noise_factor)))).alias("low"),
                (pl.col("open") * (1 + pl.lit(random.gauss(0, noise_factor)))).alias("open")
            ])

            trades = await simulate_trades_vectorized(noisy_data, strategy, RISK_REWARD_RATIOS, timeframe)

            if trades:
                total_return = sum(t['pnl_pct'] for t in trades)
                results.append(total_return)

        if results:
            mean_return = np.mean(results)
            std_return = np.std(results)
            confidence_interval = {
                'lower_95': np.percentile(results, 2.5),
                'upper_95': np.percentile(results, 97.5),
                'lower_68': np.percentile(results, 16),
                'upper_68': np.percentile(results, 84)
            }

            # Run one final deterministic backtest for trade details
            final_trades = await simulate_trades_vectorized(data, strategy, RISK_REWARD_RATIOS, timeframe)
            metrics = calculate_performance_metrics(final_trades, symbol, strategy['name'], timeframe, RISK_REWARD_RATIOS[0]) if final_trades else {}

            return {
                'mode': 'probabilistic',
                'trades': final_trades or [],
                'metrics': metrics,
                'probabilistic_results': {
                    'mean_return': mean_return,
                    'std_return': std_return,
                    'confidence_interval': confidence_interval,
                    'simulation_count': n_simulations
                }
            }

        return {'mode': 'probabilistic', 'trades': [], 'metrics': {}, 'probabilistic_results': {}}

    async def _run_adaptive_ai_backtest(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                      symbol: str, timeframe: str) -> Dict[str, Any]:
        """AI-powered adaptive backtesting with dynamic parameter optimization"""
        logger.info(f"[AI] Running adaptive AI backtest for {symbol}")

        # Split data for training and testing
        split_point = int(len(data) * 0.7)
        train_data = data[:split_point]
        test_data = data[split_point:]

        # Extract features for ML model
        features = self._extract_ml_features(train_data)

        if len(features) < 10:  # Need minimum data for ML
            return await self._run_deterministic_backtest(strategy, data, symbol, timeframe)

        # Train a simple model to predict optimal parameters
        try:
            # Prepare training data
            X = np.array([f['features'] for f in features])
            y = np.array([f['target'] for f in features])

            if len(X) > 0 and len(y) > 0:
                # Train random forest model
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)

                model = RandomForestRegressor(n_estimators=50, random_state=42)
                model.fit(X_scaled, y)

                # Use model to adapt strategy parameters
                adapted_strategy = await self._adapt_strategy_parameters(strategy, model, scaler, test_data)

                # Run backtest with adapted strategy
                trades = await simulate_trades_vectorized(test_data, adapted_strategy, RISK_REWARD_RATIOS, timeframe)
                metrics = calculate_performance_metrics(trades, symbol, strategy['name'], timeframe, RISK_REWARD_RATIOS[0]) if trades else {}

                return {
                    'mode': 'adaptive_ai',
                    'trades': trades or [],
                    'metrics': metrics,
                    'ai_adaptations': {
                        'original_strategy': strategy,
                        'adapted_strategy': adapted_strategy,
                        'model_score': model.score(X_scaled, y) if len(X_scaled) > 0 else 0,
                        'feature_importance': model.feature_importances_.tolist() if hasattr(model, 'feature_importances_') else []
                    }
                }
        except Exception as e:
            logger.warning(f"[AI] AI adaptation failed, falling back to deterministic: {e}")
            return await self._run_deterministic_backtest(strategy, data, symbol, timeframe)

        return await self._run_deterministic_backtest(strategy, data, symbol, timeframe)

    async def _run_monte_carlo_backtest(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                      symbol: str, timeframe: str) -> Dict[str, Any]:
        """Monte Carlo simulation backtesting"""
        logger.info(f"[MC] Running Monte Carlo backtest for {symbol}")

        n_simulations = self.config.monte_carlo_runs
        results = []

        for i in range(n_simulations):
            # Bootstrap sampling of historical data
            sample_indices = np.random.choice(len(data), size=len(data), replace=True)
            sampled_data = data[sample_indices]

            trades = await simulate_trades_vectorized(sampled_data, strategy, RISK_REWARD_RATIOS, timeframe)

            if trades:
                total_return = sum(t['pnl_pct'] for t in trades)
                max_dd = self._calculate_max_drawdown([t['pnl_pct'] / 100 for t in trades])
                results.append({
                    'total_return': total_return,
                    'max_drawdown': max_dd,
                    'num_trades': len(trades)
                })

        if results:
            returns = [r['total_return'] for r in results]
            drawdowns = [r['max_drawdown'] for r in results]

            monte_carlo_stats = {
                'mean_return': np.mean(returns),
                'std_return': np.std(returns),
                'var_95': np.percentile(returns, 5),
                'cvar_95': np.mean([r for r in returns if r <= np.percentile(returns, 5)]),
                'max_drawdown_95': np.percentile(drawdowns, 95),
                'probability_of_profit': sum(1 for r in returns if r > 0) / len(returns),
                'simulation_count': n_simulations
            }

            # Run deterministic backtest for baseline
            baseline_trades = await simulate_trades_vectorized(data, strategy, RISK_REWARD_RATIOS, timeframe)
            metrics = calculate_performance_metrics(baseline_trades, symbol, strategy['name'], timeframe, RISK_REWARD_RATIOS[0]) if baseline_trades else {}

            return {
                'mode': 'monte_carlo',
                'trades': baseline_trades or [],
                'metrics': metrics,
                'monte_carlo_stats': monte_carlo_stats
            }

        return {'mode': 'monte_carlo', 'trades': [], 'metrics': {}, 'monte_carlo_stats': {}}

    async def _run_walk_forward_backtest(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                       symbol: str, timeframe: str) -> Dict[str, Any]:
        """Walk-forward analysis backtesting"""
        logger.info(f"[WF] Running walk-forward backtest for {symbol}")

        window_size = min(252, len(data) // 4)  # 1 year or 1/4 of data
        step_size = window_size // 4  # 25% step

        if len(data) < window_size * 2:
            return await self._run_deterministic_backtest(strategy, data, symbol, timeframe)

        walk_forward_results = []
        all_trades = []

        for start_idx in range(0, len(data) - window_size, step_size):
            end_idx = start_idx + window_size
            window_data = data[start_idx:end_idx]

            trades = await simulate_trades_vectorized(window_data, strategy, RISK_REWARD_RATIOS, timeframe)

            if trades:
                window_return = sum(t['pnl_pct'] for t in trades)
                walk_forward_results.append({
                    'period': f"{start_idx}-{end_idx}",
                    'return': window_return,
                    'trades': len(trades)
                })
                all_trades.extend(trades)

        if walk_forward_results:
            consistency_score = 1 - (np.std([r['return'] for r in walk_forward_results]) /
                                   (abs(np.mean([r['return'] for r in walk_forward_results])) + 1e-6))

            metrics = calculate_performance_metrics(all_trades, symbol, strategy['name'], timeframe, RISK_REWARD_RATIOS[0]) if all_trades else {}

            return {
                'mode': 'walk_forward',
                'trades': all_trades,
                'metrics': metrics,
                'walk_forward_analysis': {
                    'periods': walk_forward_results,
                    'consistency_score': consistency_score,
                    'window_size': window_size,
                    'step_size': step_size
                }
            }

        return {'mode': 'walk_forward', 'trades': [], 'metrics': {}, 'walk_forward_analysis': {}}

    def _extract_ml_features(self, data: pl.DataFrame) -> List[Dict[str, Any]]:
        """Extract features for machine learning model"""
        features = []

        try:
            # Calculate technical indicators for features
            data_with_features = data.with_columns([
                pl.col("close").rolling_mean(window_size=5).alias("sma_5"),
                pl.col("close").rolling_mean(window_size=20).alias("sma_20"),
                pl.col("volume").rolling_mean(window_size=20).alias("avg_volume"),
                (pl.col("high") - pl.col("low")).alias("range"),
                (pl.col("close") - pl.col("open")).alias("body")
            ])

            # Create feature vectors
            for i in range(20, len(data_with_features) - 5):  # Need lookback and lookahead
                row = data_with_features[i]
                future_rows = data_with_features[i+1:i+6]

                if len(future_rows) == 5:
                    # Features: price ratios, volume, volatility
                    feature_vector = [
                        row['close'][0] / row['sma_5'][0] if row['sma_5'][0] else 1,
                        row['close'][0] / row['sma_20'][0] if row['sma_20'][0] else 1,
                        row['volume'][0] / row['avg_volume'][0] if row['avg_volume'][0] else 1,
                        row['range'][0] / row['close'][0] if row['close'][0] else 0,
                        abs(row['body'][0]) / row['close'][0] if row['close'][0] else 0
                    ]

                    # Target: future return
                    future_return = (future_rows[-1]['close'][0] - row['close'][0]) / row['close'][0]

                    features.append({
                        'features': feature_vector,
                        'target': future_return
                    })
        except Exception as e:
            logger.warning(f"[ML] Feature extraction failed: {e}")

        return features

    async def _adapt_strategy_parameters(self, strategy: Dict[str, Any], model, scaler, data: pl.DataFrame) -> Dict[str, Any]:
        """Adapt strategy parameters using ML model predictions"""
        adapted_strategy = copy.deepcopy(strategy)

        try:
            # Extract current market features
            current_features = self._extract_ml_features(data[-50:])  # Last 50 periods

            if current_features:
                # Get model prediction for current market conditions
                latest_features = np.array([current_features[-1]['features']])
                scaled_features = scaler.transform(latest_features)
                prediction = model.predict(scaled_features)[0]

                # Adapt strategy based on prediction
                if prediction > 0.01:  # Bullish prediction
                    # More aggressive long signals, less aggressive short signals
                    if 'long' in adapted_strategy:
                        adapted_strategy['long'] = adapted_strategy['long'].replace('> 50', '> 45')
                    if 'short' in adapted_strategy:
                        adapted_strategy['short'] = adapted_strategy['short'].replace('< 50', '< 45')
                elif prediction < -0.01:  # Bearish prediction
                    # More aggressive short signals, less aggressive long signals
                    if 'long' in adapted_strategy:
                        adapted_strategy['long'] = adapted_strategy['long'].replace('> 50', '> 55')
                    if 'short' in adapted_strategy:
                        adapted_strategy['short'] = adapted_strategy['short'].replace('< 50', '< 55')

                logger.info(f"[AI] Adapted strategy based on prediction: {prediction:.4f}")
        except Exception as e:
            logger.warning(f"[AI] Strategy adaptation failed: {e}")

        return adapted_strategy

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 3] ENHANCED PERFORMANCE METRICS
# ═══════════════════════════════════════════════════════════════════════════════

class AdvancedMetricsCalculator:
    """Comprehensive performance metrics calculation engine"""

    def __init__(self, benchmark_return: float = 0.12):  # 12% annual benchmark
        self.benchmark_return = benchmark_return
        self.risk_free_rate = 0.06  # 6% risk-free rate

    async def calculate_comprehensive_metrics(self, trades: List[Dict[str, Any]],
                                            equity_curve: pl.DataFrame,
                                            market_data: pl.DataFrame,
                                            symbol: str, strategy_name: str) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics"""
        if not trades:
            return self._empty_metrics()

        logger.info(f"[METRICS] Calculating comprehensive metrics for {symbol}")

        # Basic trade metrics
        basic_metrics = self._calculate_basic_metrics(trades)

        # Risk metrics
        risk_metrics = self._calculate_risk_metrics(trades, equity_curve)

        # Statistical metrics
        statistical_metrics = self._calculate_statistical_metrics(trades)

        # Market-relative metrics
        market_metrics = await self._calculate_market_relative_metrics(trades, market_data)

        # Regime-based metrics
        regime_metrics = await self._calculate_regime_metrics(trades, market_data)

        # Advanced risk metrics
        advanced_risk_metrics = self._calculate_advanced_risk_metrics(trades, equity_curve)

        # Combine all metrics
        comprehensive_metrics = {
            'symbol': symbol,
            'strategy_name': strategy_name,
            'calculation_timestamp': datetime.now().isoformat(),
            'basic_metrics': basic_metrics,
            'risk_metrics': risk_metrics,
            'statistical_metrics': statistical_metrics,
            'market_metrics': market_metrics,
            'regime_metrics': regime_metrics,
            'advanced_risk_metrics': advanced_risk_metrics
        }

        return comprehensive_metrics

    def _calculate_basic_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate basic trading metrics"""
        if not trades:
            return {}

        returns = [t['pnl_pct'] / 100 for t in trades]
        pnl_values = [t['pnl'] for t in trades]

        winning_trades = [r for r in returns if r > 0]
        losing_trades = [r for r in returns if r < 0]

        return {
            'total_trades': len(trades),
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': len(winning_trades) / len(trades) if trades else 0,
            'total_return': sum(returns),
            'average_return': np.mean(returns),
            'average_win': np.mean(winning_trades) if winning_trades else 0,
            'average_loss': np.mean(losing_trades) if losing_trades else 0,
            'profit_factor': sum(winning_trades) / abs(sum(losing_trades)) if losing_trades else float('inf'),
            'expectancy': np.mean(returns),
            'largest_win': max(returns) if returns else 0,
            'largest_loss': min(returns) if returns else 0,
            'total_pnl': sum(pnl_values)
        }

    def _calculate_risk_metrics(self, trades: List[Dict[str, Any]], equity_curve: pl.DataFrame) -> Dict[str, float]:
        """Calculate risk-related metrics"""
        if not trades:
            return {}

        returns = [t['pnl_pct'] / 100 for t in trades]

        # Volatility metrics
        volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0
        downside_returns = [r for r in returns if r < 0]
        downside_volatility = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 1 else 0

        # Drawdown metrics
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = running_max - cumulative_returns
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0

        # Calculate drawdown duration
        drawdown_periods = []
        in_drawdown = False
        drawdown_start = 0

        for i, dd in enumerate(drawdowns):
            if dd > 0 and not in_drawdown:
                in_drawdown = True
                drawdown_start = i
            elif dd == 0 and in_drawdown:
                in_drawdown = False
                drawdown_periods.append(i - drawdown_start)

        avg_drawdown_duration = np.mean(drawdown_periods) if drawdown_periods else 0
        max_drawdown_duration = max(drawdown_periods) if drawdown_periods else 0

        # Risk ratios
        mean_return = np.mean(returns)
        sharpe_ratio = (mean_return * 252 - self.risk_free_rate) / volatility if volatility > 0 else 0
        sortino_ratio = (mean_return * 252 - self.risk_free_rate) / downside_volatility if downside_volatility > 0 else 0
        calmar_ratio = (mean_return * 252) / max_drawdown if max_drawdown > 0 else 0

        return {
            'volatility': volatility,
            'downside_volatility': downside_volatility,
            'max_drawdown': max_drawdown,
            'avg_drawdown_duration': avg_drawdown_duration,
            'max_drawdown_duration': max_drawdown_duration,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'var_95': np.percentile(returns, 5),
            'cvar_95': np.mean([r for r in returns if r <= np.percentile(returns, 5)]) if returns else 0
        }

    def _calculate_statistical_metrics(self, trades: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate statistical significance metrics"""
        if not trades or len(trades) < 2:
            return {}

        returns = [t['pnl_pct'] / 100 for t in trades]

        # Statistical tests
        t_stat, p_value = stats.ttest_1samp(returns, 0)

        # Distribution metrics
        skewness = stats.skew(returns)
        kurtosis = stats.kurtosis(returns)

        # Normality test
        shapiro_stat, shapiro_p = stats.shapiro(returns) if len(returns) <= 5000 else (0, 0)

        # Autocorrelation (if enough data)
        autocorr = 0
        if len(returns) > 10:
            autocorr = np.corrcoef(returns[:-1], returns[1:])[0, 1] if not np.isnan(np.corrcoef(returns[:-1], returns[1:])[0, 1]) else 0

        return {
            't_statistic': t_stat,
            'p_value': p_value,
            'is_significant': p_value < 0.05,
            'skewness': skewness,
            'kurtosis': kurtosis,
            'is_normal_distribution': shapiro_p > 0.05 if shapiro_p > 0 else False,
            'autocorrelation': autocorr,
            'sample_size': len(returns),
            'confidence_level_95': {
                'lower': np.percentile(returns, 2.5),
                'upper': np.percentile(returns, 97.5)
            }
        }

    async def _calculate_market_relative_metrics(self, trades: List[Dict[str, Any]],
                                               market_data: pl.DataFrame) -> Dict[str, float]:
        """Calculate metrics relative to market performance"""
        if not trades or len(market_data) == 0:
            return {}

        try:
            # Calculate market returns
            market_returns = market_data.select([
                ((pl.col("close") - pl.col("close").shift(1)) / pl.col("close").shift(1)).alias("market_return")
            ]).drop_nulls()

            if len(market_returns) == 0:
                return {}

            market_return_values = market_returns['market_return'].to_list()
            strategy_returns = [t['pnl_pct'] / 100 for t in trades]

            # Align lengths for correlation calculation
            min_length = min(len(strategy_returns), len(market_return_values))
            if min_length < 2:
                return {}

            strategy_aligned = strategy_returns[:min_length]
            market_aligned = market_return_values[:min_length]

            # Calculate beta and alpha
            covariance = np.cov(strategy_aligned, market_aligned)[0, 1]
            market_variance = np.var(market_aligned)
            beta = covariance / market_variance if market_variance > 0 else 0

            strategy_mean = np.mean(strategy_aligned) * 252
            market_mean = np.mean(market_aligned) * 252
            alpha = strategy_mean - (self.risk_free_rate + beta * (market_mean - self.risk_free_rate))

            # Correlation
            correlation = np.corrcoef(strategy_aligned, market_aligned)[0, 1] if not np.isnan(np.corrcoef(strategy_aligned, market_aligned)[0, 1]) else 0

            # Information ratio
            excess_returns = np.array(strategy_aligned) - np.array(market_aligned)
            tracking_error = np.std(excess_returns) * np.sqrt(252) if len(excess_returns) > 1 else 0
            information_ratio = np.mean(excess_returns) * 252 / tracking_error if tracking_error > 0 else 0

            # Treynor ratio
            treynor_ratio = (strategy_mean - self.risk_free_rate) / beta if beta > 0 else 0

            return {
                'beta': beta,
                'alpha': alpha,
                'correlation_to_market': correlation,
                'information_ratio': information_ratio,
                'treynor_ratio': treynor_ratio,
                'tracking_error': tracking_error,
                'excess_return': strategy_mean - market_mean
            }
        except Exception as e:
            logger.warning(f"[METRICS] Market relative metrics calculation failed: {e}")
            return {}

    async def _calculate_regime_metrics(self, trades: List[Dict[str, Any]],
                                      market_data: pl.DataFrame) -> Dict[str, Any]:
        """Calculate performance metrics by market regime"""
        if not trades or len(market_data) == 0:
            return {}

        try:
            # Detect market regimes based on volatility and trend
            market_data_with_regime = market_data.with_columns([
                pl.col("close").rolling_mean(window_size=20).alias("sma_20"),
                pl.col("close").rolling_std(window_size=20).alias("volatility_20")
            ])

            # Define regime conditions
            regime_data = market_data_with_regime.with_columns([
                pl.when(pl.col("volatility_20") > pl.col("volatility_20").quantile(0.75))
                .then(pl.lit("high_volatility"))
                .when(pl.col("volatility_20") < pl.col("volatility_20").quantile(0.25))
                .then(pl.lit("low_volatility"))
                .when(pl.col("close") > pl.col("sma_20") * 1.02)
                .then(pl.lit("bull"))
                .when(pl.col("close") < pl.col("sma_20") * 0.98)
                .then(pl.lit("bear"))
                .otherwise(pl.lit("sideways"))
                .alias("regime")
            ])

            # Group trades by regime (simplified - would need timestamp matching in real implementation)
            regime_performance = {}
            regime_counts = regime_data.group_by("regime").count()

            for regime_row in regime_counts.iter_rows(named=True):
                regime = regime_row["regime"]
                count = regime_row["count"]

                # Simplified: assign trades proportionally to regimes
                regime_trades = trades[:int(len(trades) * count / len(regime_data))]

                if regime_trades:
                    regime_returns = [t['pnl_pct'] / 100 for t in regime_trades]
                    regime_performance[regime] = {
                        'total_return': sum(regime_returns),
                        'avg_return': np.mean(regime_returns),
                        'volatility': np.std(regime_returns) * np.sqrt(252) if len(regime_returns) > 1 else 0,
                        'win_rate': sum(1 for r in regime_returns if r > 0) / len(regime_returns),
                        'trade_count': len(regime_trades)
                    }

            return {
                'regime_performance': regime_performance,
                'regime_exposure': {regime: perf['trade_count'] / len(trades) for regime, perf in regime_performance.items()},
                'best_regime': max(regime_performance.keys(), key=lambda x: regime_performance[x]['total_return']) if regime_performance else None,
                'worst_regime': min(regime_performance.keys(), key=lambda x: regime_performance[x]['total_return']) if regime_performance else None
            }
        except Exception as e:
            logger.warning(f"[METRICS] Regime metrics calculation failed: {e}")
            return {}

    def _calculate_advanced_risk_metrics(self, trades: List[Dict[str, Any]],
                                       equity_curve: pl.DataFrame) -> Dict[str, float]:
        """Calculate advanced risk metrics"""
        if not trades:
            return {}

        returns = [t['pnl_pct'] / 100 for t in trades]

        # Tail risk metrics
        var_99 = np.percentile(returns, 1)
        cvar_99 = np.mean([r for r in returns if r <= var_99]) if any(r <= var_99 for r in returns) else 0

        # Maximum adverse excursion and maximum favorable excursion
        mae_values = []
        mfe_values = []

        for trade in trades:
            # Simplified MAE/MFE calculation
            entry_price = trade.get('entry_price', 0)
            exit_price = trade.get('exit_price', 0)

            if entry_price > 0:
                mae = min(0, (exit_price - entry_price) / entry_price)  # Worst point
                mfe = max(0, (exit_price - entry_price) / entry_price)  # Best point
                mae_values.append(abs(mae))
                mfe_values.append(mfe)

        avg_mae = np.mean(mae_values) if mae_values else 0
        avg_mfe = np.mean(mfe_values) if mfe_values else 0

        # Ulcer Index (alternative to standard deviation)
        if len(returns) > 1:
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (running_max - cumulative_returns) / (running_max + 1e-6)
            ulcer_index = np.sqrt(np.mean(drawdowns ** 2))
        else:
            ulcer_index = 0

        # Pain Index
        pain_index = np.mean([abs(r) for r in returns if r < 0]) if any(r < 0 for r in returns) else 0

        # Recovery factor
        total_return = sum(returns)
        max_drawdown = self._calculate_max_drawdown_simple(returns)
        recovery_factor = total_return / max_drawdown if max_drawdown > 0 else 0

        return {
            'var_99': var_99,
            'cvar_99': cvar_99,
            'average_mae': avg_mae,
            'average_mfe': avg_mfe,
            'ulcer_index': ulcer_index,
            'pain_index': pain_index,
            'recovery_factor': recovery_factor,
            'tail_ratio': abs(np.percentile(returns, 95)) / abs(np.percentile(returns, 5)) if np.percentile(returns, 5) != 0 else 0
        }

    def _calculate_max_drawdown_simple(self, returns: List[float]) -> float:
        """Simple max drawdown calculation"""
        if not returns:
            return 0.0

        cumulative = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdowns = running_max - cumulative
        return np.max(drawdowns) if len(drawdowns) > 0 else 0.0

    def _empty_metrics(self) -> Dict[str, Any]:
        """Return empty metrics structure"""
        return {
            'basic_metrics': {},
            'risk_metrics': {},
            'statistical_metrics': {},
            'market_metrics': {},
            'regime_metrics': {},
            'advanced_risk_metrics': {}
        }

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 4] CAPITAL & RISK MODELING
# ═══════════════════════════════════════════════════════════════════════════════

class CapitalRiskManager:
    """Advanced capital allocation and risk management system"""

    def __init__(self, initial_capital: float, risk_model: RiskModel = RiskModel.FIXED_POSITION):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_model = risk_model
        self.position_history = []
        self.risk_budget = 0.02  # 2% risk per trade
        self.max_portfolio_risk = 0.10  # 10% max portfolio risk
        self.volatility_target = 0.15  # 15% annual volatility target

    async def calculate_position_size(self, signal_strength: float, volatility: float,
                                    price: float, strategy_performance: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate optimal position size based on risk model"""

        if self.risk_model == RiskModel.FIXED_POSITION:
            return self._fixed_position_sizing(price)
        elif self.risk_model == RiskModel.KELLY_CRITERION:
            return await self._kelly_criterion_sizing(signal_strength, strategy_performance, price)
        elif self.risk_model == RiskModel.VOLATILITY_TARGET:
            return self._volatility_target_sizing(volatility, price)
        elif self.risk_model == RiskModel.RISK_PARITY:
            return self._risk_parity_sizing(volatility, price)
        elif self.risk_model == RiskModel.MAX_DRAWDOWN_TARGET:
            return self._max_drawdown_target_sizing(strategy_performance, price)
        else:
            return self._fixed_position_sizing(price)

    def _fixed_position_sizing(self, price: float) -> Dict[str, Any]:
        """Fixed percentage position sizing"""
        position_value = self.current_capital * 0.1  # 10% of capital
        quantity = int(position_value / price) if price > 0 else 0

        return {
            'quantity': quantity,
            'position_value': quantity * price,
            'position_pct': 0.1,
            'risk_amount': position_value * self.risk_budget,
            'method': 'fixed_position'
        }

    async def _kelly_criterion_sizing(self, signal_strength: float,
                                    strategy_performance: Dict[str, Any], price: float) -> Dict[str, Any]:
        """Kelly Criterion optimal position sizing"""
        try:
            # Extract performance metrics
            win_rate = strategy_performance.get('win_rate', 0.5)
            avg_win = strategy_performance.get('avg_win', 0.02)
            avg_loss = strategy_performance.get('avg_loss', -0.01)

            if avg_loss >= 0:  # Avoid division by zero
                avg_loss = -0.01

            # Kelly formula: f = (bp - q) / b
            # where b = avg_win/|avg_loss|, p = win_rate, q = 1-win_rate
            b = avg_win / abs(avg_loss)
            p = win_rate
            q = 1 - win_rate

            kelly_fraction = (b * p - q) / b

            # Apply signal strength and safety constraints
            kelly_fraction *= signal_strength  # Scale by signal confidence
            kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%

            position_value = self.current_capital * kelly_fraction
            quantity = int(position_value / price) if price > 0 else 0

            return {
                'quantity': quantity,
                'position_value': quantity * price,
                'position_pct': kelly_fraction,
                'risk_amount': position_value * abs(avg_loss),
                'method': 'kelly_criterion',
                'kelly_fraction': kelly_fraction,
                'signal_strength': signal_strength
            }
        except Exception as e:
            logger.warning(f"[RISK] Kelly criterion calculation failed: {e}")
            return self._fixed_position_sizing(price)

    def _volatility_target_sizing(self, volatility: float, price: float) -> Dict[str, Any]:
        """Volatility targeting position sizing"""
        if volatility <= 0:
            volatility = 0.20  # Default 20% volatility

        # Scale position size inversely with volatility
        volatility_scalar = self.volatility_target / volatility
        base_position_pct = 0.1  # 10% base allocation

        adjusted_position_pct = base_position_pct * volatility_scalar
        adjusted_position_pct = max(0.01, min(adjusted_position_pct, 0.30))  # 1% to 30%

        position_value = self.current_capital * adjusted_position_pct
        quantity = int(position_value / price) if price > 0 else 0

        return {
            'quantity': quantity,
            'position_value': quantity * price,
            'position_pct': adjusted_position_pct,
            'risk_amount': position_value * volatility,
            'method': 'volatility_target',
            'volatility_scalar': volatility_scalar,
            'target_volatility': self.volatility_target
        }

    def _risk_parity_sizing(self, volatility: float, price: float) -> Dict[str, Any]:
        """Risk parity position sizing"""
        if volatility <= 0:
            volatility = 0.20

        # Equal risk contribution across positions
        target_risk_contribution = self.risk_budget
        position_pct = target_risk_contribution / volatility
        position_pct = max(0.01, min(position_pct, 0.25))  # 1% to 25%

        position_value = self.current_capital * position_pct
        quantity = int(position_value / price) if price > 0 else 0

        return {
            'quantity': quantity,
            'position_value': quantity * price,
            'position_pct': position_pct,
            'risk_amount': position_value * volatility,
            'method': 'risk_parity',
            'risk_contribution': target_risk_contribution
        }

    def _max_drawdown_target_sizing(self, strategy_performance: Dict[str, Any], price: float) -> Dict[str, Any]:
        """Position sizing based on maximum drawdown target"""
        max_drawdown = strategy_performance.get('max_drawdown', 0.05)

        if max_drawdown <= 0:
            max_drawdown = 0.05  # Default 5%

        # Scale position size to limit portfolio drawdown
        target_max_dd = 0.15  # 15% max portfolio drawdown
        dd_scalar = target_max_dd / max_drawdown
        base_position_pct = 0.1

        adjusted_position_pct = base_position_pct * dd_scalar
        adjusted_position_pct = max(0.01, min(adjusted_position_pct, 0.20))  # 1% to 20%

        position_value = self.current_capital * adjusted_position_pct
        quantity = int(position_value / price) if price > 0 else 0

        return {
            'quantity': quantity,
            'position_value': quantity * price,
            'position_pct': adjusted_position_pct,
            'risk_amount': position_value * max_drawdown,
            'method': 'max_drawdown_target',
            'drawdown_scalar': dd_scalar,
            'strategy_max_dd': max_drawdown
        }

    async def update_capital(self, trade_pnl: float, trade_info: Dict[str, Any]):
        """Update capital after trade execution"""
        self.current_capital += trade_pnl

        # Record position in history
        self.position_history.append({
            'timestamp': datetime.now(),
            'trade_pnl': trade_pnl,
            'capital_before': self.current_capital - trade_pnl,
            'capital_after': self.current_capital,
            'position_info': trade_info
        })

        # Trim history to last 1000 trades
        if len(self.position_history) > 1000:
            self.position_history = self.position_history[-1000:]

    def get_portfolio_risk_metrics(self) -> Dict[str, Any]:
        """Calculate current portfolio risk metrics"""
        if not self.position_history:
            return {}

        # Calculate portfolio-level metrics
        returns = []
        capital_values = []

        for position in self.position_history:
            if position['capital_before'] > 0:
                return_pct = position['trade_pnl'] / position['capital_before']
                returns.append(return_pct)
                capital_values.append(position['capital_after'])

        if not returns:
            return {}

        # Risk metrics
        portfolio_volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0

        # Drawdown calculation
        peak_capital = self.initial_capital
        current_drawdown = 0
        max_drawdown = 0

        for capital in capital_values:
            if capital > peak_capital:
                peak_capital = capital

            current_drawdown = (peak_capital - capital) / peak_capital
            max_drawdown = max(max_drawdown, current_drawdown)

        # Risk budget utilization
        total_risk_taken = sum(pos['position_info'].get('risk_amount', 0) for pos in self.position_history[-10:])
        avg_risk_per_trade = total_risk_taken / min(10, len(self.position_history))
        risk_budget_utilization = avg_risk_per_trade / (self.current_capital * self.risk_budget)

        return {
            'current_capital': self.current_capital,
            'total_return': (self.current_capital - self.initial_capital) / self.initial_capital,
            'portfolio_volatility': portfolio_volatility,
            'current_drawdown': current_drawdown,
            'max_drawdown': max_drawdown,
            'risk_budget_utilization': risk_budget_utilization,
            'total_trades': len(self.position_history),
            'avg_position_size': np.mean([pos['position_info'].get('position_pct', 0) for pos in self.position_history]) if self.position_history else 0
        }

    def adjust_risk_parameters(self, performance_metrics: Dict[str, Any]):
        """Dynamically adjust risk parameters based on performance"""
        current_dd = performance_metrics.get('current_drawdown', 0)
        volatility = performance_metrics.get('portfolio_volatility', 0)

        # Reduce risk if drawdown is high
        if current_dd > 0.10:  # 10% drawdown
            self.risk_budget *= 0.8  # Reduce risk by 20%
            logger.info(f"[RISK] Reducing risk budget due to drawdown: {self.risk_budget:.3f}")

        # Increase risk if performance is good and volatility is low
        elif current_dd < 0.02 and volatility < self.volatility_target * 0.8:
            self.risk_budget = min(self.risk_budget * 1.1, 0.05)  # Increase risk, cap at 5%
            logger.info(f"[RISK] Increasing risk budget due to good performance: {self.risk_budget:.3f}")

        # Ensure risk budget stays within bounds
        self.risk_budget = max(0.005, min(self.risk_budget, 0.05))  # 0.5% to 5%

class PortfolioOptimizer:
    """Portfolio optimization and allocation engine"""

    def __init__(self, strategies: List[Dict[str, Any]]):
        self.strategies = strategies
        self.correlation_matrix = None
        self.expected_returns = {}
        self.volatilities = {}

    async def optimize_portfolio_allocation(self, historical_performance: Dict[str, Any]) -> Dict[str, float]:
        """Optimize portfolio allocation across strategies"""
        logger.info("[PORTFOLIO] Optimizing strategy allocation...")

        # Extract performance data for each strategy
        strategy_returns = {}

        for strategy_id, performance in historical_performance.items():
            if 'returns' in performance and len(performance['returns']) > 0:
                returns = performance['returns']
                strategy_returns[strategy_id] = {
                    'mean_return': np.mean(returns),
                    'volatility': np.std(returns),
                    'sharpe_ratio': np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0
                }

        if len(strategy_returns) < 2:
            # Equal allocation if insufficient data
            equal_weight = 1.0 / len(self.strategies)
            return {strategy['name']: equal_weight for strategy in self.strategies}

        # Calculate correlation matrix
        correlation_matrix = await self._calculate_correlation_matrix(historical_performance)

        # Optimize using mean-variance optimization (simplified)
        optimal_weights = self._mean_variance_optimization(strategy_returns, correlation_matrix)

        return optimal_weights

    async def _calculate_correlation_matrix(self, historical_performance: Dict[str, Any]) -> np.ndarray:
        """Calculate correlation matrix between strategies"""
        strategy_ids = list(historical_performance.keys())
        n_strategies = len(strategy_ids)

        correlation_matrix = np.eye(n_strategies)

        for i, strategy1 in enumerate(strategy_ids):
            for j, strategy2 in enumerate(strategy_ids):
                if i != j:
                    returns1 = historical_performance[strategy1].get('returns', [])
                    returns2 = historical_performance[strategy2].get('returns', [])

                    if len(returns1) > 1 and len(returns2) > 1:
                        min_length = min(len(returns1), len(returns2))
                        corr = np.corrcoef(returns1[:min_length], returns2[:min_length])[0, 1]
                        correlation_matrix[i, j] = corr if not np.isnan(corr) else 0

        return correlation_matrix

    def _mean_variance_optimization(self, strategy_returns: Dict[str, Any],
                                  correlation_matrix: np.ndarray) -> Dict[str, float]:
        """Simplified mean-variance optimization"""
        strategy_ids = list(strategy_returns.keys())
        n_strategies = len(strategy_ids)

        if n_strategies == 0:
            return {}

        # Extract expected returns and volatilities
        expected_returns = np.array([strategy_returns[sid]['mean_return'] for sid in strategy_ids])
        volatilities = np.array([strategy_returns[sid]['volatility'] for sid in strategy_ids])

        # Create covariance matrix
        cov_matrix = np.outer(volatilities, volatilities) * correlation_matrix

        # Simple optimization: maximize Sharpe ratio
        try:
            # Inverse volatility weighting as a simple heuristic
            inv_vol_weights = 1 / (volatilities + 1e-6)
            weights = inv_vol_weights / np.sum(inv_vol_weights)

            # Ensure weights sum to 1 and are non-negative
            weights = np.maximum(weights, 0)
            weights = weights / np.sum(weights)

            return {strategy_ids[i]: weights[i] for i in range(n_strategies)}
        except Exception as e:
            logger.warning(f"[PORTFOLIO] Optimization failed, using equal weights: {e}")
            equal_weight = 1.0 / n_strategies
            return {sid: equal_weight for sid in strategy_ids}

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 5] SCENARIO & REGIME-BASED TESTING
# ═══════════════════════════════════════════════════════════════════════════════

class MarketRegimeAnalyzer:
    """Advanced market regime detection and analysis"""

    def __init__(self):
        self.regime_history = []
        self.regime_thresholds = {
            'volatility_high': 0.75,  # 75th percentile
            'volatility_low': 0.25,   # 25th percentile
            'trend_bull': 0.02,       # 2% above moving average
            'trend_bear': -0.02       # 2% below moving average
        }

    async def detect_market_regime(self, market_data: pl.DataFrame,
                                 lookback_period: int = 252) -> Dict[str, Any]:
        """Detect current market regime based on multiple factors"""
        if len(market_data) < lookback_period:
            return {'regime': MarketRegime.SIDEWAYS, 'confidence': 0.5, 'factors': {}}

        # Calculate regime indicators
        regime_factors = await self._calculate_regime_factors(market_data, lookback_period)

        # Determine primary regime
        primary_regime = self._classify_regime(regime_factors)

        # Calculate confidence score
        confidence = self._calculate_regime_confidence(regime_factors)

        regime_info = {
            'regime': primary_regime,
            'confidence': confidence,
            'factors': regime_factors,
            'timestamp': datetime.now(),
            'lookback_period': lookback_period
        }

        # Update regime history
        self.regime_history.append(regime_info)
        if len(self.regime_history) > 1000:
            self.regime_history = self.regime_history[-1000:]

        return regime_info

    async def _calculate_regime_factors(self, data: pl.DataFrame, lookback: int) -> Dict[str, float]:
        """Calculate various factors for regime classification"""
        recent_data = data.tail(lookback)

        # Price-based factors
        price_data = recent_data.with_columns([
            pl.col("close").rolling_mean(window_size=20).alias("sma_20"),
            pl.col("close").rolling_mean(window_size=50).alias("sma_50"),
            pl.col("close").rolling_std(window_size=20).alias("volatility_20"),
            ((pl.col("close") - pl.col("close").shift(1)) / pl.col("close").shift(1)).alias("returns")
        ]).drop_nulls()

        if len(price_data) == 0:
            return {}

        # Trend factors
        current_price = price_data['close'][-1]
        sma_20 = price_data['sma_20'][-1]
        sma_50 = price_data['sma_50'][-1]

        trend_strength = (current_price - sma_20) / sma_20 if sma_20 > 0 else 0
        trend_direction = 1 if sma_20 > sma_50 else -1

        # Volatility factors
        returns = price_data['returns'].to_list()
        current_volatility = np.std(returns[-20:]) * np.sqrt(252) if len(returns) >= 20 else 0
        volatility_percentile = stats.percentileofscore(price_data['volatility_20'].to_list(),
                                                       price_data['volatility_20'][-1]) / 100

        # Momentum factors
        momentum_5d = (current_price - price_data['close'][-6]) / price_data['close'][-6] if len(price_data) >= 6 else 0
        momentum_20d = (current_price - price_data['close'][-21]) / price_data['close'][-21] if len(price_data) >= 21 else 0

        # Volume factors (if available)
        volume_trend = 0
        if 'volume' in price_data.columns:
            recent_volume = np.mean(price_data['volume'][-5:].to_list())
            avg_volume = np.mean(price_data['volume'].to_list())
            volume_trend = (recent_volume - avg_volume) / avg_volume if avg_volume > 0 else 0

        return {
            'trend_strength': trend_strength,
            'trend_direction': trend_direction,
            'volatility_percentile': volatility_percentile,
            'current_volatility': current_volatility,
            'momentum_5d': momentum_5d,
            'momentum_20d': momentum_20d,
            'volume_trend': volume_trend,
            'price_vs_sma20': trend_strength,
            'sma20_vs_sma50': (sma_20 - sma_50) / sma_50 if sma_50 > 0 else 0
        }

    def _classify_regime(self, factors: Dict[str, float]) -> MarketRegime:
        """Classify market regime based on factors"""
        if not factors:
            return MarketRegime.SIDEWAYS

        volatility_pct = factors.get('volatility_percentile', 0.5)
        trend_strength = factors.get('trend_strength', 0)
        momentum_20d = factors.get('momentum_20d', 0)

        # High volatility regime
        if volatility_pct > self.regime_thresholds['volatility_high']:
            return MarketRegime.HIGH_VOLATILITY

        # Low volatility regime
        elif volatility_pct < self.regime_thresholds['volatility_low']:
            return MarketRegime.LOW_VOLATILITY

        # Trending regimes
        elif abs(trend_strength) > 0.05 and abs(momentum_20d) > 0.10:
            if trend_strength > 0 and momentum_20d > 0:
                return MarketRegime.BULL
            elif trend_strength < 0 and momentum_20d < 0:
                return MarketRegime.BEAR
            else:
                return MarketRegime.TRENDING

        # Default to sideways
        else:
            return MarketRegime.SIDEWAYS

    def _calculate_regime_confidence(self, factors: Dict[str, float]) -> float:
        """Calculate confidence score for regime classification"""
        if not factors:
            return 0.5

        # Factors that increase confidence
        trend_consistency = abs(factors.get('trend_strength', 0)) * abs(factors.get('momentum_20d', 0))
        volatility_extremeness = abs(factors.get('volatility_percentile', 0.5) - 0.5) * 2
        momentum_alignment = 1 if (factors.get('momentum_5d', 0) * factors.get('momentum_20d', 0)) > 0 else 0

        # Combine factors
        confidence = (trend_consistency * 0.4 + volatility_extremeness * 0.3 + momentum_alignment * 0.3)
        return max(0.1, min(confidence, 0.95))  # Bound between 10% and 95%

class ScenarioTester:
    """Scenario analysis and stress testing engine"""

    def __init__(self):
        self.scenarios = self._define_scenarios()

    def _define_scenarios(self) -> Dict[str, Dict[str, Any]]:
        """Define various market scenarios for testing"""
        return {
            'market_crash': {
                'description': 'Severe market decline (-30% over 3 months)',
                'price_shock': -0.30,
                'volatility_multiplier': 3.0,
                'duration_days': 90,
                'recovery_days': 180
            },
            'flash_crash': {
                'description': 'Sudden market drop (-10% in 1 day)',
                'price_shock': -0.10,
                'volatility_multiplier': 5.0,
                'duration_days': 1,
                'recovery_days': 30
            },
            'bull_market': {
                'description': 'Strong bull market (+50% over 1 year)',
                'price_shock': 0.50,
                'volatility_multiplier': 0.8,
                'duration_days': 252,
                'recovery_days': 0
            },
            'high_volatility': {
                'description': 'Extended high volatility period',
                'price_shock': 0.0,
                'volatility_multiplier': 2.5,
                'duration_days': 126,
                'recovery_days': 63
            },
            'low_volatility': {
                'description': 'Extended low volatility period',
                'price_shock': 0.0,
                'volatility_multiplier': 0.3,
                'duration_days': 126,
                'recovery_days': 63
            },
            'interest_rate_shock': {
                'description': 'Sudden interest rate increase',
                'price_shock': -0.15,
                'volatility_multiplier': 2.0,
                'duration_days': 30,
                'recovery_days': 90
            }
        }

    async def run_scenario_analysis(self, strategy: Dict[str, Any],
                                  base_data: pl.DataFrame,
                                  scenarios: List[str] = None) -> Dict[str, Any]:
        """Run strategy against multiple scenarios"""
        if scenarios is None:
            scenarios = list(self.scenarios.keys())

        logger.info(f"[SCENARIO] Running scenario analysis for {len(scenarios)} scenarios")

        scenario_results = {}

        for scenario_name in scenarios:
            if scenario_name not in self.scenarios:
                continue

            scenario_config = self.scenarios[scenario_name]

            # Generate scenario data
            scenario_data = await self._generate_scenario_data(base_data, scenario_config)

            # Run backtest on scenario data
            trades = await simulate_trades_vectorized(scenario_data, strategy, RISK_REWARD_RATIOS, "1D")

            # Calculate scenario-specific metrics
            scenario_metrics = self._calculate_scenario_metrics(trades, scenario_config)

            scenario_results[scenario_name] = {
                'scenario_config': scenario_config,
                'trades': trades,
                'metrics': scenario_metrics,
                'data_points': len(scenario_data)
            }

        # Calculate cross-scenario analysis
        cross_scenario_analysis = self._analyze_cross_scenario_performance(scenario_results)

        return {
            'scenario_results': scenario_results,
            'cross_scenario_analysis': cross_scenario_analysis,
            'total_scenarios': len(scenarios),
            'analysis_timestamp': datetime.now().isoformat()
        }

    async def _generate_scenario_data(self, base_data: pl.DataFrame,
                                    scenario_config: Dict[str, Any]) -> pl.DataFrame:
        """Generate market data for a specific scenario"""
        scenario_data = base_data.clone()

        price_shock = scenario_config.get('price_shock', 0)
        volatility_multiplier = scenario_config.get('volatility_multiplier', 1.0)
        duration_days = scenario_config.get('duration_days', 30)

        # Apply scenario modifications
        if len(scenario_data) > duration_days:
            # Apply shock to a portion of the data
            shock_start = len(scenario_data) // 2
            shock_end = min(shock_start + duration_days, len(scenario_data))

            # Calculate price adjustments
            shock_data = scenario_data[shock_start:shock_end]

            # Apply gradual price shock
            shock_progression = np.linspace(0, price_shock, len(shock_data))
            volatility_adjustment = np.random.normal(0, volatility_multiplier - 1, len(shock_data))

            # Modify prices
            modified_prices = []
            for i, (shock, vol_adj) in enumerate(zip(shock_progression, volatility_adjustment)):
                base_price = shock_data[i]['close'][0]
                shocked_price = base_price * (1 + shock + vol_adj * 0.01)
                modified_prices.append(shocked_price)

            # Update scenario data
            scenario_data = scenario_data.with_columns([
                pl.when(pl.int_range(len(scenario_data)).is_between(shock_start, shock_end - 1))
                .then(pl.Series(modified_prices + [scenario_data[shock_end:]['close'].to_list()]))
                .otherwise(pl.col("close"))
                .alias("close")
            ])

        return scenario_data

    def _calculate_scenario_metrics(self, trades: List[Dict[str, Any]],
                                  scenario_config: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate metrics specific to scenario testing"""
        if not trades:
            return {'total_return': 0, 'max_drawdown': 0, 'trade_count': 0}

        returns = [t['pnl_pct'] / 100 for t in trades]

        # Basic metrics
        total_return = sum(returns)
        max_drawdown = self._calculate_max_drawdown_simple(returns)

        # Scenario-specific metrics
        worst_trade = min(returns) if returns else 0
        best_trade = max(returns) if returns else 0

        # Stress test metrics
        stress_score = self._calculate_stress_score(returns, scenario_config)

        return {
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'trade_count': len(trades),
            'worst_trade': worst_trade,
            'best_trade': best_trade,
            'stress_score': stress_score,
            'scenario_survival': total_return > -0.50,  # Survived if loss < 50%
            'recovery_potential': max(0, total_return + max_drawdown)  # Ability to recover
        }

    def _calculate_stress_score(self, returns: List[float], scenario_config: Dict[str, Any]) -> float:
        """Calculate how well strategy handles stress scenario"""
        if not returns:
            return 0.0

        # Factors for stress scoring
        total_return = sum(returns)
        max_drawdown = self._calculate_max_drawdown_simple(returns)
        volatility = np.std(returns) if len(returns) > 1 else 0

        # Expected stress based on scenario
        expected_stress = abs(scenario_config.get('price_shock', 0)) * scenario_config.get('volatility_multiplier', 1)

        # Score components (0-1 scale, higher is better)
        return_score = max(0, (total_return + 0.5) / 0.5)  # Normalize around -50% to 0%
        drawdown_score = max(0, (0.3 - max_drawdown) / 0.3)  # Good if drawdown < 30%
        stability_score = max(0, (0.5 - volatility) / 0.5)  # Good if volatility < 50%

        # Weighted stress score
        stress_score = (return_score * 0.4 + drawdown_score * 0.4 + stability_score * 0.2)
        return max(0, min(stress_score, 1))

    def _analyze_cross_scenario_performance(self, scenario_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze performance across all scenarios"""
        if not scenario_results:
            return {}

        # Collect metrics across scenarios
        scenario_returns = {}
        scenario_drawdowns = {}
        scenario_stress_scores = {}

        for scenario_name, result in scenario_results.items():
            metrics = result.get('metrics', {})
            scenario_returns[scenario_name] = metrics.get('total_return', 0)
            scenario_drawdowns[scenario_name] = metrics.get('max_drawdown', 0)
            scenario_stress_scores[scenario_name] = metrics.get('stress_score', 0)

        # Cross-scenario analysis
        avg_return = np.mean(list(scenario_returns.values()))
        worst_scenario = min(scenario_returns.keys(), key=lambda x: scenario_returns[x])
        best_scenario = max(scenario_returns.keys(), key=lambda x: scenario_returns[x])

        # Robustness metrics
        return_consistency = 1 - (np.std(list(scenario_returns.values())) / (abs(avg_return) + 1e-6))
        survival_rate = sum(1 for r in scenario_returns.values() if r > -0.50) / len(scenario_returns)
        avg_stress_score = np.mean(list(scenario_stress_scores.values()))

        return {
            'average_return': avg_return,
            'worst_scenario': worst_scenario,
            'best_scenario': best_scenario,
            'worst_scenario_return': scenario_returns[worst_scenario],
            'best_scenario_return': scenario_returns[best_scenario],
            'return_consistency': return_consistency,
            'survival_rate': survival_rate,
            'average_stress_score': avg_stress_score,
            'scenario_rankings': sorted(scenario_returns.items(), key=lambda x: x[1], reverse=True)
        }

    def _calculate_max_drawdown_simple(self, returns: List[float]) -> float:
        """Simple max drawdown calculation"""
        if not returns:
            return 0.0

        cumulative = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdowns = running_max - cumulative
        return np.max(drawdowns) if len(drawdowns) > 0 else 0.0

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 6] PARAMETER SWEEP & OPTIMIZATION
# ═══════════════════════════════════════════════════════════════════════════════

class ParameterOptimizer:
    """Advanced parameter optimization engine with multiple algorithms"""

    def __init__(self, optimization_method: OptimizationMethod = OptimizationMethod.GENETIC_ALGORITHM):
        self.optimization_method = optimization_method
        self.optimization_history = []
        self.best_parameters = {}
        self.overfitting_protection = True

    async def optimize_strategy_parameters(self, strategy: Dict[str, Any],
                                         data: pl.DataFrame,
                                         parameter_ranges: Dict[str, tuple],
                                         optimization_target: str = 'sharpe_ratio',
                                         max_iterations: int = 100) -> Dict[str, Any]:
        """Optimize strategy parameters using specified method"""

        logger.info(f"[OPTIMIZE] Starting {self.optimization_method.value} optimization")
        logger.info(f"[OPTIMIZE] Target: {optimization_target}, Max iterations: {max_iterations}")

        # Split data for in-sample and out-of-sample testing
        split_point = int(len(data) * 0.7)
        train_data = data[:split_point]
        test_data = data[split_point:]

        if self.optimization_method == OptimizationMethod.GRID_SEARCH:
            result = await self._grid_search_optimization(strategy, train_data, parameter_ranges,
                                                        optimization_target, max_iterations)
        elif self.optimization_method == OptimizationMethod.GENETIC_ALGORITHM:
            result = await self._genetic_algorithm_optimization(strategy, train_data, parameter_ranges,
                                                              optimization_target, max_iterations)
        elif self.optimization_method == OptimizationMethod.BAYESIAN:
            result = await self._bayesian_optimization(strategy, train_data, parameter_ranges,
                                                     optimization_target, max_iterations)
        elif self.optimization_method == OptimizationMethod.RANDOM_SEARCH:
            result = await self._random_search_optimization(strategy, train_data, parameter_ranges,
                                                           optimization_target, max_iterations)
        else:
            result = await self._grid_search_optimization(strategy, train_data, parameter_ranges,
                                                        optimization_target, max_iterations)

        # Validate on out-of-sample data
        if self.overfitting_protection and len(test_data) > 0:
            oos_validation = await self._validate_out_of_sample(result['best_strategy'], test_data, optimization_target)
            result['out_of_sample_validation'] = oos_validation

        # Store optimization history
        self.optimization_history.append(result)
        self.best_parameters = result['best_parameters']

        return result

    async def _grid_search_optimization(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                      parameter_ranges: Dict[str, tuple], target: str,
                                      max_iterations: int) -> Dict[str, Any]:
        """Grid search parameter optimization"""
        logger.info("[OPTIMIZE] Running grid search optimization")

        # Generate parameter grid
        param_grid = self._generate_parameter_grid(parameter_ranges, max_iterations)

        best_score = float('-inf')
        best_params = {}
        best_strategy = strategy.copy()
        all_results = []

        for i, params in enumerate(param_grid):
            if i >= max_iterations:
                break

            # Create strategy with current parameters
            current_strategy = self._apply_parameters_to_strategy(strategy, params)

            # Run backtest
            trades = await simulate_trades_vectorized(data, current_strategy, RISK_REWARD_RATIOS, "1D")

            # Calculate target metric
            score = self._calculate_optimization_score(trades, target)

            result = {
                'iteration': i,
                'parameters': params,
                'score': score,
                'trade_count': len(trades) if trades else 0
            }
            all_results.append(result)

            # Update best if improved
            if score > best_score:
                best_score = score
                best_params = params.copy()
                best_strategy = current_strategy.copy()

            if i % 10 == 0:
                logger.info(f"[OPTIMIZE] Grid search progress: {i}/{min(len(param_grid), max_iterations)}, Best score: {best_score:.4f}")

        return {
            'method': 'grid_search',
            'best_score': best_score,
            'best_parameters': best_params,
            'best_strategy': best_strategy,
            'optimization_results': all_results,
            'total_iterations': len(all_results),
            'target_metric': target
        }

    async def _genetic_algorithm_optimization(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                            parameter_ranges: Dict[str, tuple], target: str,
                                            max_iterations: int) -> Dict[str, Any]:
        """Genetic algorithm parameter optimization"""
        logger.info("[OPTIMIZE] Running genetic algorithm optimization")

        population_size = min(20, max_iterations // 5)
        generations = max_iterations // population_size
        mutation_rate = 0.1
        crossover_rate = 0.8

        # Initialize population
        population = []
        for _ in range(population_size):
            individual = self._generate_random_parameters(parameter_ranges)
            population.append(individual)

        best_score = float('-inf')
        best_params = {}
        best_strategy = strategy.copy()
        generation_history = []

        for generation in range(generations):
            # Evaluate population
            fitness_scores = []
            for individual in population:
                current_strategy = self._apply_parameters_to_strategy(strategy, individual)
                trades = await simulate_trades_vectorized(data, current_strategy, RISK_REWARD_RATIOS, "1D")
                score = self._calculate_optimization_score(trades, target)
                fitness_scores.append(score)

                # Update best
                if score > best_score:
                    best_score = score
                    best_params = individual.copy()
                    best_strategy = current_strategy.copy()

            # Record generation stats
            generation_stats = {
                'generation': generation,
                'best_score': max(fitness_scores),
                'avg_score': np.mean(fitness_scores),
                'worst_score': min(fitness_scores)
            }
            generation_history.append(generation_stats)

            # Selection, crossover, and mutation
            new_population = []

            # Keep best individuals (elitism)
            elite_count = max(1, population_size // 10)
            elite_indices = np.argsort(fitness_scores)[-elite_count:]
            for idx in elite_indices:
                new_population.append(population[idx].copy())

            # Generate offspring
            while len(new_population) < population_size:
                # Tournament selection
                parent1 = self._tournament_selection(population, fitness_scores)
                parent2 = self._tournament_selection(population, fitness_scores)

                # Crossover
                if random.random() < crossover_rate:
                    child1, child2 = self._crossover(parent1, parent2, parameter_ranges)
                else:
                    child1, child2 = parent1.copy(), parent2.copy()

                # Mutation
                if random.random() < mutation_rate:
                    child1 = self._mutate(child1, parameter_ranges)
                if random.random() < mutation_rate:
                    child2 = self._mutate(child2, parameter_ranges)

                new_population.extend([child1, child2])

            population = new_population[:population_size]

            if generation % 5 == 0:
                logger.info(f"[OPTIMIZE] GA Generation {generation}/{generations}, Best score: {best_score:.4f}")

        return {
            'method': 'genetic_algorithm',
            'best_score': best_score,
            'best_parameters': best_params,
            'best_strategy': best_strategy,
            'generation_history': generation_history,
            'total_iterations': generations * population_size,
            'target_metric': target,
            'population_size': population_size,
            'generations': generations
        }

    async def _bayesian_optimization(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                   parameter_ranges: Dict[str, tuple], target: str,
                                   max_iterations: int) -> Dict[str, Any]:
        """Bayesian optimization (simplified implementation)"""
        logger.info("[OPTIMIZE] Running Bayesian optimization")

        # Simplified Bayesian optimization using random sampling with exploitation/exploration
        best_score = float('-inf')
        best_params = {}
        best_strategy = strategy.copy()
        all_results = []

        # Initial random exploration
        exploration_iterations = min(10, max_iterations // 3)

        for i in range(max_iterations):
            if i < exploration_iterations:
                # Exploration phase: random sampling
                params = self._generate_random_parameters(parameter_ranges)
            else:
                # Exploitation phase: sample around best known parameters
                params = self._sample_around_best(best_params, parameter_ranges, exploration_factor=0.1)

            current_strategy = self._apply_parameters_to_strategy(strategy, params)
            trades = await simulate_trades_vectorized(data, current_strategy, RISK_REWARD_RATIOS, "1D")
            score = self._calculate_optimization_score(trades, target)

            result = {
                'iteration': i,
                'parameters': params,
                'score': score,
                'phase': 'exploration' if i < exploration_iterations else 'exploitation'
            }
            all_results.append(result)

            if score > best_score:
                best_score = score
                best_params = params.copy()
                best_strategy = current_strategy.copy()

            if i % 10 == 0:
                logger.info(f"[OPTIMIZE] Bayesian optimization progress: {i}/{max_iterations}, Best score: {best_score:.4f}")

        return {
            'method': 'bayesian_optimization',
            'best_score': best_score,
            'best_parameters': best_params,
            'best_strategy': best_strategy,
            'optimization_results': all_results,
            'total_iterations': len(all_results),
            'target_metric': target
        }

    async def _random_search_optimization(self, strategy: Dict[str, Any], data: pl.DataFrame,
                                        parameter_ranges: Dict[str, tuple], target: str,
                                        max_iterations: int) -> Dict[str, Any]:
        """Random search parameter optimization"""
        logger.info("[OPTIMIZE] Running random search optimization")

        best_score = float('-inf')
        best_params = {}
        best_strategy = strategy.copy()
        all_results = []

        for i in range(max_iterations):
            params = self._generate_random_parameters(parameter_ranges)
            current_strategy = self._apply_parameters_to_strategy(strategy, params)
            trades = await simulate_trades_vectorized(data, current_strategy, RISK_REWARD_RATIOS, "1D")
            score = self._calculate_optimization_score(trades, target)

            result = {
                'iteration': i,
                'parameters': params,
                'score': score
            }
            all_results.append(result)

            if score > best_score:
                best_score = score
                best_params = params.copy()
                best_strategy = current_strategy.copy()

            if i % 20 == 0:
                logger.info(f"[OPTIMIZE] Random search progress: {i}/{max_iterations}, Best score: {best_score:.4f}")

        return {
            'method': 'random_search',
            'best_score': best_score,
            'best_parameters': best_params,
            'best_strategy': best_strategy,
            'optimization_results': all_results,
            'total_iterations': len(all_results),
            'target_metric': target
        }

    def _generate_parameter_grid(self, parameter_ranges: Dict[str, tuple], max_combinations: int) -> List[Dict[str, Any]]:
        """Generate parameter grid for grid search"""
        param_names = list(parameter_ranges.keys())
        param_values = []

        for param_name, (min_val, max_val, param_type) in parameter_ranges.items():
            if param_type == 'int':
                # For integers, create reasonable number of steps
                steps = min(10, max_val - min_val + 1)
                values = list(range(min_val, max_val + 1, max(1, (max_val - min_val) // steps)))
            elif param_type == 'float':
                # For floats, create 10 steps
                steps = 10
                values = [min_val + i * (max_val - min_val) / (steps - 1) for i in range(steps)]
            else:
                # For other types, assume it's a list of discrete values
                values = [min_val, max_val]  # Simplified

            param_values.append(values)

        # Generate all combinations
        import itertools
        combinations = list(itertools.product(*param_values))

        # Limit combinations if too many
        if len(combinations) > max_combinations:
            step = len(combinations) // max_combinations
            combinations = combinations[::step]

        # Convert to list of dictionaries
        param_grid = []
        for combination in combinations:
            param_dict = {param_names[i]: combination[i] for i in range(len(param_names))}
            param_grid.append(param_dict)

        return param_grid

    def _generate_random_parameters(self, parameter_ranges: Dict[str, tuple]) -> Dict[str, Any]:
        """Generate random parameters within specified ranges"""
        params = {}

        for param_name, (min_val, max_val, param_type) in parameter_ranges.items():
            if param_type == 'int':
                params[param_name] = random.randint(min_val, max_val)
            elif param_type == 'float':
                params[param_name] = random.uniform(min_val, max_val)
            else:
                # For discrete values
                params[param_name] = random.choice([min_val, max_val])

        return params

    def _sample_around_best(self, best_params: Dict[str, Any], parameter_ranges: Dict[str, tuple],
                          exploration_factor: float = 0.1) -> Dict[str, Any]:
        """Sample parameters around the best known parameters"""
        if not best_params:
            return self._generate_random_parameters(parameter_ranges)

        params = {}

        for param_name, (min_val, max_val, param_type) in parameter_ranges.items():
            if param_name in best_params:
                best_val = best_params[param_name]

                if param_type == 'int':
                    range_size = max_val - min_val
                    noise = int(range_size * exploration_factor)
                    new_val = best_val + random.randint(-noise, noise)
                    params[param_name] = max(min_val, min(max_val, new_val))
                elif param_type == 'float':
                    range_size = max_val - min_val
                    noise = range_size * exploration_factor
                    new_val = best_val + random.uniform(-noise, noise)
                    params[param_name] = max(min_val, min(max_val, new_val))
                else:
                    params[param_name] = random.choice([min_val, max_val])
            else:
                params[param_name] = self._generate_random_parameters({param_name: parameter_ranges[param_name]})[param_name]

        return params

    def _apply_parameters_to_strategy(self, strategy: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Apply parameters to strategy configuration"""
        modified_strategy = strategy.copy()

        # Simple parameter application - in real implementation, this would be more sophisticated
        for param_name, param_value in parameters.items():
            if param_name in ['rsi_period', 'sma_period', 'ema_period']:
                # Replace period values in strategy conditions
                for condition_type in ['long', 'short', 'exit_long', 'exit_short']:
                    if condition_type in modified_strategy:
                        condition = modified_strategy[condition_type]
                        if param_name == 'rsi_period' and 'rsi' in condition.lower():
                            # Update RSI period in condition string
                            modified_strategy[condition_type] = condition.replace('rsi(14)', f'rsi({param_value})')
                        elif param_name == 'sma_period' and 'sma' in condition.lower():
                            modified_strategy[condition_type] = condition.replace('sma(20)', f'sma({param_value})')
                        elif param_name == 'ema_period' and 'ema' in condition.lower():
                            modified_strategy[condition_type] = condition.replace('ema(12)', f'ema({param_value})')

            # Store parameter for reference
            if 'parameters' not in modified_strategy:
                modified_strategy['parameters'] = {}
            modified_strategy['parameters'][param_name] = param_value

        return modified_strategy

    def _calculate_optimization_score(self, trades: List[Dict[str, Any]], target_metric: str) -> float:
        """Calculate optimization score based on target metric"""
        if not trades:
            return float('-inf')

        returns = [t['pnl_pct'] / 100 for t in trades]

        if target_metric == 'total_return':
            return sum(returns)
        elif target_metric == 'sharpe_ratio':
            if len(returns) < 2:
                return 0
            mean_return = np.mean(returns) * 252  # Annualized
            volatility = np.std(returns) * np.sqrt(252)
            return (mean_return - 0.06) / volatility if volatility > 0 else 0  # Risk-free rate = 6%
        elif target_metric == 'sortino_ratio':
            if len(returns) < 2:
                return 0
            mean_return = np.mean(returns) * 252
            downside_returns = [r for r in returns if r < 0]
            downside_volatility = np.std(downside_returns) * np.sqrt(252) if downside_returns else 0.01
            return (mean_return - 0.06) / downside_volatility
        elif target_metric == 'calmar_ratio':
            total_return = sum(returns)
            max_dd = self._calculate_max_drawdown_simple(returns)
            return total_return / max_dd if max_dd > 0 else 0
        elif target_metric == 'win_rate':
            winning_trades = sum(1 for r in returns if r > 0)
            return winning_trades / len(returns)
        elif target_metric == 'profit_factor':
            winning_returns = [r for r in returns if r > 0]
            losing_returns = [r for r in returns if r < 0]
            total_wins = sum(winning_returns) if winning_returns else 0
            total_losses = abs(sum(losing_returns)) if losing_returns else 0.01
            return total_wins / total_losses
        else:
            return sum(returns)  # Default to total return

    def _tournament_selection(self, population: List[Dict[str, Any]], fitness_scores: List[float],
                            tournament_size: int = 3) -> Dict[str, Any]:
        """Tournament selection for genetic algorithm"""
        tournament_indices = random.sample(range(len(population)), min(tournament_size, len(population)))
        tournament_fitness = [fitness_scores[i] for i in tournament_indices]
        winner_idx = tournament_indices[np.argmax(tournament_fitness)]
        return population[winner_idx].copy()

    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any],
                  parameter_ranges: Dict[str, tuple]) -> tuple:
        """Crossover operation for genetic algorithm"""
        child1 = parent1.copy()
        child2 = parent2.copy()

        # Single-point crossover
        param_names = list(parameter_ranges.keys())
        if len(param_names) > 1:
            crossover_point = random.randint(1, len(param_names) - 1)

            for i in range(crossover_point, len(param_names)):
                param_name = param_names[i]
                if param_name in parent1 and param_name in parent2:
                    child1[param_name] = parent2[param_name]
                    child2[param_name] = parent1[param_name]

        return child1, child2

    def _mutate(self, individual: Dict[str, Any], parameter_ranges: Dict[str, tuple]) -> Dict[str, Any]:
        """Mutation operation for genetic algorithm"""
        mutated = individual.copy()

        # Mutate one random parameter
        param_names = list(parameter_ranges.keys())
        if param_names:
            param_to_mutate = random.choice(param_names)
            min_val, max_val, param_type = parameter_ranges[param_to_mutate]

            if param_type == 'int':
                mutated[param_to_mutate] = random.randint(min_val, max_val)
            elif param_type == 'float':
                mutated[param_to_mutate] = random.uniform(min_val, max_val)
            else:
                mutated[param_to_mutate] = random.choice([min_val, max_val])

        return mutated

    async def _validate_out_of_sample(self, optimized_strategy: Dict[str, Any],
                                    test_data: pl.DataFrame, target_metric: str) -> Dict[str, Any]:
        """Validate optimized strategy on out-of-sample data"""
        logger.info("[OPTIMIZE] Validating on out-of-sample data")

        # Run backtest on test data
        trades = await simulate_trades_vectorized(test_data, optimized_strategy, RISK_REWARD_RATIOS, "1D")

        # Calculate metrics
        oos_score = self._calculate_optimization_score(trades, target_metric)

        # Calculate degradation from in-sample performance
        in_sample_score = getattr(self, '_last_in_sample_score', 0)
        degradation = (in_sample_score - oos_score) / abs(in_sample_score) if in_sample_score != 0 else 0

        # Overfitting detection
        is_overfitted = degradation > 0.3  # More than 30% degradation suggests overfitting

        return {
            'out_of_sample_score': oos_score,
            'in_sample_score': in_sample_score,
            'degradation_pct': degradation,
            'is_overfitted': is_overfitted,
            'trade_count': len(trades) if trades else 0,
            'validation_data_points': len(test_data)
        }

    def _calculate_max_drawdown_simple(self, returns: List[float]) -> float:
        """Simple max drawdown calculation"""
        if not returns:
            return 0.0

        cumulative = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdowns = running_max - cumulative
        return np.max(drawdowns) if len(drawdowns) > 0 else 0.0

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 7] RESULT LOGGING & VERSIONING
# ═══════════════════════════════════════════════════════════════════════════════

class BacktestLogger:
    """Comprehensive result logging and experiment management system"""

    def __init__(self, base_path: str = "backtest_results"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)
        self.experiment_id = self._generate_experiment_id()
        self.experiment_path = self.base_path / self.experiment_id
        self.experiment_path.mkdir(exist_ok=True)

        # Initialize logging components
        self.metadata = {}
        self.results_database = []
        self.version_history = []

        logger.info(f"[LOGGER] Initialized experiment: {self.experiment_id}")

    def _generate_experiment_id(self) -> str:
        """Generate unique experiment ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=6))
        return f"exp_{timestamp}_{random_suffix}"

    async def log_backtest_run(self, strategy: Dict[str, Any], results: Dict[str, Any],
                             config: BacktestConfig, metadata: Dict[str, Any] = None) -> str:
        """Log a complete backtest run"""
        run_id = self._generate_run_id()

        # Prepare comprehensive log entry
        log_entry = {
            'run_id': run_id,
            'experiment_id': self.experiment_id,
            'timestamp': datetime.now().isoformat(),
            'strategy': strategy,
            'config': {
                'mode': config.mode.value,
                'risk_model': config.risk_model.value,
                'initial_capital': config.initial_capital,
                'monte_carlo_runs': config.monte_carlo_runs
            },
            'results': results,
            'metadata': metadata or {},
            'system_info': self._get_system_info(),
            'data_hash': self._calculate_data_hash(results),
            'version': self._get_code_version()
        }

        # Save to multiple formats
        await self._save_run_json(run_id, log_entry)
        await self._save_run_csv(run_id, log_entry)
        await self._update_experiment_summary(log_entry)

        # Add to in-memory database
        self.results_database.append(log_entry)

        logger.info(f"[LOGGER] Logged backtest run: {run_id}")
        return run_id

    def _generate_run_id(self) -> str:
        """Generate unique run ID"""
        timestamp = datetime.now().strftime("%H%M%S")
        counter = len(self.results_database) + 1
        return f"run_{timestamp}_{counter:04d}"

    async def _save_run_json(self, run_id: str, log_entry: Dict[str, Any]):
        """Save run data as JSON"""
        json_path = self.experiment_path / f"{run_id}.json"

        # Convert non-serializable objects
        serializable_entry = self._make_serializable(log_entry)

        with open(json_path, 'w') as f:
            json.dump(serializable_entry, f, indent=2, default=str)

    async def _save_run_csv(self, run_id: str, log_entry: Dict[str, Any]):
        """Save run summary as CSV"""
        csv_path = self.experiment_path / "runs_summary.csv"

        # Extract key metrics for CSV
        results = log_entry.get('results', {})
        metrics = results.get('metrics', {})

        csv_row = {
            'run_id': run_id,
            'timestamp': log_entry['timestamp'],
            'strategy_name': log_entry['strategy'].get('name', 'Unknown'),
            'mode': log_entry['config']['mode'],
            'total_trades': len(results.get('trades', [])),
            'total_return': metrics.get('total_return', 0),
            'win_rate': metrics.get('win_rate', 0),
            'sharpe_ratio': metrics.get('sharpe_ratio', 0),
            'max_drawdown': metrics.get('max_drawdown', 0),
            'profit_factor': metrics.get('profit_factor', 0)
        }

        # Append to CSV file
        df = pl.DataFrame([csv_row])

        if csv_path.exists():
            existing_df = pl.read_csv(csv_path)
            combined_df = pl.concat([existing_df, df])
        else:
            combined_df = df

        combined_df.write_csv(csv_path)

    async def _update_experiment_summary(self, log_entry: Dict[str, Any]):
        """Update experiment-level summary"""
        summary_path = self.experiment_path / "experiment_summary.json"

        # Load existing summary or create new
        if summary_path.exists():
            with open(summary_path, 'r') as f:
                summary = json.load(f)
        else:
            summary = {
                'experiment_id': self.experiment_id,
                'created_at': datetime.now().isoformat(),
                'total_runs': 0,
                'strategies_tested': set(),
                'best_performance': {},
                'run_history': []
            }

        # Update summary
        summary['total_runs'] += 1
        summary['last_updated'] = datetime.now().isoformat()

        # Track strategies
        strategy_name = log_entry['strategy'].get('name', 'Unknown')
        if isinstance(summary['strategies_tested'], list):
            summary['strategies_tested'] = set(summary['strategies_tested'])
        summary['strategies_tested'].add(strategy_name)

        # Track best performance
        results = log_entry.get('results', {})
        metrics = results.get('metrics', {})
        current_return = metrics.get('total_return', 0)

        if 'total_return' not in summary['best_performance'] or current_return > summary['best_performance'].get('total_return', float('-inf')):
            summary['best_performance'] = {
                'run_id': log_entry['run_id'],
                'strategy_name': strategy_name,
                'total_return': current_return,
                'timestamp': log_entry['timestamp']
            }

        # Add to run history
        summary['run_history'].append({
            'run_id': log_entry['run_id'],
            'timestamp': log_entry['timestamp'],
            'strategy_name': strategy_name,
            'total_return': current_return
        })

        # Convert set to list for JSON serialization
        summary['strategies_tested'] = list(summary['strategies_tested'])

        # Save updated summary
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

    def _make_serializable(self, obj: Any) -> Any:
        """Convert objects to JSON-serializable format"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        else:
            return obj

    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for reproducibility"""
        return {
            'python_version': sys.version,
            'platform': sys.platform,
            'timestamp': datetime.now().isoformat(),
            'working_directory': str(Path.cwd()),
            'polars_version': pl.__version__ if hasattr(pl, '__version__') else 'unknown'
        }

    def _calculate_data_hash(self, results: Dict[str, Any]) -> str:
        """Calculate hash of results for integrity checking"""
        import hashlib

        # Create a simplified hash based on key results
        trades = results.get('trades', [])
        hash_data = f"{len(trades)}_{datetime.now().date()}"

        return hashlib.md5(hash_data.encode()).hexdigest()[:8]

    def _get_code_version(self) -> str:
        """Get code version information"""
        # In a real implementation, this would integrate with git
        return f"v1.0.0_{datetime.now().strftime('%Y%m%d')}"

    async def query_results(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Query logged results with filters"""
        results = self.results_database.copy()

        if not filters:
            return results

        # Apply filters
        filtered_results = []
        for result in results:
            match = True

            # Strategy name filter
            if 'strategy_name' in filters:
                strategy_name = result['strategy'].get('name', '')
                if filters['strategy_name'].lower() not in strategy_name.lower():
                    match = False

            # Date range filter
            if 'date_from' in filters:
                result_date = datetime.fromisoformat(result['timestamp'])
                filter_date = datetime.fromisoformat(filters['date_from'])
                if result_date < filter_date:
                    match = False

            if 'date_to' in filters:
                result_date = datetime.fromisoformat(result['timestamp'])
                filter_date = datetime.fromisoformat(filters['date_to'])
                if result_date > filter_date:
                    match = False

            # Performance filter
            if 'min_return' in filters:
                total_return = result['results'].get('metrics', {}).get('total_return', 0)
                if total_return < filters['min_return']:
                    match = False

            if match:
                filtered_results.append(result)

        return filtered_results

    async def export_results(self, format: str = 'csv', filters: Dict[str, Any] = None) -> str:
        """Export results in specified format"""
        results = await self.query_results(filters)

        if format.lower() == 'csv':
            return await self._export_csv(results)
        elif format.lower() == 'json':
            return await self._export_json(results)
        elif format.lower() == 'excel':
            return await self._export_excel(results)
        else:
            raise ValueError(f"Unsupported export format: {format}")

    async def _export_csv(self, results: List[Dict[str, Any]]) -> str:
        """Export results to CSV"""
        if not results:
            return ""

        # Flatten results for CSV
        csv_data = []
        for result in results:
            metrics = result['results'].get('metrics', {})
            row = {
                'run_id': result['run_id'],
                'timestamp': result['timestamp'],
                'strategy_name': result['strategy'].get('name', ''),
                'mode': result['config']['mode'],
                'total_trades': len(result['results'].get('trades', [])),
                'total_return': metrics.get('total_return', 0),
                'win_rate': metrics.get('win_rate', 0),
                'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                'max_drawdown': metrics.get('max_drawdown', 0),
                'profit_factor': metrics.get('profit_factor', 0)
            }
            csv_data.append(row)

        # Create DataFrame and save
        df = pl.DataFrame(csv_data)
        export_path = self.experiment_path / f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.write_csv(export_path)

        return str(export_path)

    async def _export_json(self, results: List[Dict[str, Any]]) -> str:
        """Export results to JSON"""
        export_path = self.experiment_path / f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        serializable_results = [self._make_serializable(result) for result in results]

        with open(export_path, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)

        return str(export_path)

    async def _export_excel(self, results: List[Dict[str, Any]]) -> str:
        """Export results to Excel (simplified - would need openpyxl in real implementation)"""
        # For now, export as CSV with .xlsx extension
        csv_path = await self._export_csv(results)
        excel_path = csv_path.replace('.csv', '.xlsx')

        # In real implementation, would convert to actual Excel format
        logger.info(f"[LOGGER] Excel export saved as CSV: {excel_path}")
        return excel_path

    async def compare_runs(self, run_ids: List[str]) -> Dict[str, Any]:
        """Compare multiple backtest runs"""
        runs = []
        for run_id in run_ids:
            run_data = next((r for r in self.results_database if r['run_id'] == run_id), None)
            if run_data:
                runs.append(run_data)

        if len(runs) < 2:
            return {'error': 'Need at least 2 runs for comparison'}

        # Extract metrics for comparison
        comparison = {
            'runs_compared': len(runs),
            'run_ids': run_ids,
            'metrics_comparison': {},
            'best_performer': {},
            'worst_performer': {}
        }

        # Compare key metrics
        metrics_to_compare = ['total_return', 'win_rate', 'sharpe_ratio', 'max_drawdown', 'profit_factor']

        for metric in metrics_to_compare:
            values = []
            for run in runs:
                value = run['results'].get('metrics', {}).get(metric, 0)
                values.append({
                    'run_id': run['run_id'],
                    'strategy_name': run['strategy'].get('name', ''),
                    'value': value
                })

            # Sort by value (descending for most metrics, ascending for drawdown)
            reverse_sort = metric != 'max_drawdown'
            sorted_values = sorted(values, key=lambda x: x['value'], reverse=reverse_sort)

            comparison['metrics_comparison'][metric] = {
                'best': sorted_values[0],
                'worst': sorted_values[-1],
                'all_values': sorted_values
            }

        # Overall best/worst based on total return
        total_returns = [(run['run_id'], run['results'].get('metrics', {}).get('total_return', 0)) for run in runs]
        best_run_id, best_return = max(total_returns, key=lambda x: x[1])
        worst_run_id, worst_return = min(total_returns, key=lambda x: x[1])

        comparison['best_performer'] = {
            'run_id': best_run_id,
            'total_return': best_return
        }
        comparison['worst_performer'] = {
            'run_id': worst_run_id,
            'total_return': worst_return
        }

        return comparison

    def get_experiment_summary(self) -> Dict[str, Any]:
        """Get comprehensive experiment summary"""
        summary_path = self.experiment_path / "experiment_summary.json"

        if summary_path.exists():
            with open(summary_path, 'r') as f:
                return json.load(f)
        else:
            return {
                'experiment_id': self.experiment_id,
                'total_runs': len(self.results_database),
                'created_at': datetime.now().isoformat()
            }

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 8] BACKTEST VISUALIZATION & DEBUGGING
# ═══════════════════════════════════════════════════════════════════════════════

class BacktestVisualizer:
    """Advanced visualization and debugging tools for backtesting"""

    def __init__(self, output_dir: str = "visualizations"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.charts_created = []

    async def create_comprehensive_dashboard(self, backtest_results: Dict[str, Any],
                                           market_data: pl.DataFrame,
                                           strategy_name: str) -> Dict[str, str]:
        """Create comprehensive visualization dashboard"""
        logger.info(f"[VIZ] Creating dashboard for {strategy_name}")

        dashboard_files = {}

        # 1. Equity curve chart
        equity_chart = await self._create_equity_curve_chart(backtest_results, strategy_name)
        dashboard_files['equity_curve'] = equity_chart

        # 2. Drawdown chart
        drawdown_chart = await self._create_drawdown_chart(backtest_results, strategy_name)
        dashboard_files['drawdown'] = drawdown_chart

        # 3. Trade distribution chart
        trade_dist_chart = await self._create_trade_distribution_chart(backtest_results, strategy_name)
        dashboard_files['trade_distribution'] = trade_dist_chart

        # 4. Performance metrics table
        metrics_table = await self._create_metrics_table(backtest_results, strategy_name)
        dashboard_files['metrics_table'] = metrics_table

        # 5. Monthly returns heatmap
        monthly_returns = await self._create_monthly_returns_heatmap(backtest_results, strategy_name)
        dashboard_files['monthly_returns'] = monthly_returns

        # 6. Trade timeline chart
        timeline_chart = await self._create_trade_timeline_chart(backtest_results, market_data, strategy_name)
        dashboard_files['trade_timeline'] = timeline_chart

        # 7. Risk metrics chart
        risk_chart = await self._create_risk_metrics_chart(backtest_results, strategy_name)
        dashboard_files['risk_metrics'] = risk_chart

        # Create HTML dashboard
        dashboard_html = await self._create_html_dashboard(dashboard_files, strategy_name)
        dashboard_files['dashboard'] = dashboard_html

        logger.info(f"[VIZ] Dashboard created with {len(dashboard_files)} components")
        return dashboard_files

    async def _create_equity_curve_chart(self, results: Dict[str, Any], strategy_name: str) -> str:
        """Create equity curve visualization"""
        trades = results.get('trades', [])
        if not trades:
            return ""

        # Calculate cumulative returns
        cumulative_pnl = []
        running_total = 0
        dates = []

        for i, trade in enumerate(trades):
            running_total += trade.get('pnl', 0)
            cumulative_pnl.append(running_total)
            dates.append(f"Trade_{i+1}")

        # Create simple text-based chart data
        chart_data = {
            'title': f'Equity Curve - {strategy_name}',
            'x_axis': dates,
            'y_axis': cumulative_pnl,
            'chart_type': 'line',
            'description': f'Cumulative P&L over {len(trades)} trades'
        }

        # Save chart data as JSON (in real implementation, would create actual charts)
        chart_file = self.output_dir / f"equity_curve_{strategy_name.replace(' ', '_')}.json"
        with open(chart_file, 'w') as f:
            json.dump(chart_data, f, indent=2)

        return str(chart_file)

    async def _create_drawdown_chart(self, results: Dict[str, Any], strategy_name: str) -> str:
        """Create drawdown visualization"""
        trades = results.get('trades', [])
        if not trades:
            return ""

        # Calculate drawdown series
        cumulative_returns = []
        running_total = 0
        peak = 0
        drawdowns = []

        for trade in trades:
            running_total += trade.get('pnl_pct', 0) / 100
            cumulative_returns.append(running_total)

            if running_total > peak:
                peak = running_total

            drawdown = (peak - running_total) / (peak + 1e-6) if peak > 0 else 0
            drawdowns.append(-drawdown * 100)  # Negative for visualization

        chart_data = {
            'title': f'Drawdown Analysis - {strategy_name}',
            'x_axis': [f"Trade_{i+1}" for i in range(len(trades))],
            'y_axis': drawdowns,
            'chart_type': 'area',
            'description': f'Drawdown percentage over time',
            'max_drawdown': min(drawdowns) if drawdowns else 0
        }

        chart_file = self.output_dir / f"drawdown_{strategy_name.replace(' ', '_')}.json"
        with open(chart_file, 'w') as f:
            json.dump(chart_data, f, indent=2)

        return str(chart_file)

    async def _create_trade_distribution_chart(self, results: Dict[str, Any], strategy_name: str) -> str:
        """Create trade P&L distribution chart"""
        trades = results.get('trades', [])
        if not trades:
            return ""

        # Analyze trade distribution
        pnl_values = [trade.get('pnl_pct', 0) for trade in trades]

        # Create bins for histogram
        bins = [-10, -5, -2, -1, 0, 1, 2, 5, 10]  # Percentage bins
        bin_counts = [0] * (len(bins) - 1)
        bin_labels = []

        for i in range(len(bins) - 1):
            count = sum(1 for pnl in pnl_values if bins[i] <= pnl < bins[i+1])
            bin_counts[i] = count
            bin_labels.append(f"{bins[i]}% to {bins[i+1]}%")

        chart_data = {
            'title': f'Trade P&L Distribution - {strategy_name}',
            'x_axis': bin_labels,
            'y_axis': bin_counts,
            'chart_type': 'bar',
            'description': f'Distribution of {len(trades)} trades by P&L percentage',
            'statistics': {
                'total_trades': len(trades),
                'winning_trades': sum(1 for pnl in pnl_values if pnl > 0),
                'losing_trades': sum(1 for pnl in pnl_values if pnl < 0),
                'avg_win': np.mean([pnl for pnl in pnl_values if pnl > 0]) if any(pnl > 0 for pnl in pnl_values) else 0,
                'avg_loss': np.mean([pnl for pnl in pnl_values if pnl < 0]) if any(pnl < 0 for pnl in pnl_values) else 0
            }
        }

        chart_file = self.output_dir / f"trade_distribution_{strategy_name.replace(' ', '_')}.json"
        with open(chart_file, 'w') as f:
            json.dump(chart_data, f, indent=2)

        return str(chart_file)

    async def _create_metrics_table(self, results: Dict[str, Any], strategy_name: str) -> str:
        """Create performance metrics table"""
        metrics = results.get('metrics', {})

        # Organize metrics into categories
        table_data = {
            'title': f'Performance Metrics - {strategy_name}',
            'categories': {
                'Basic Metrics': {
                    'Total Return': f"{metrics.get('total_return', 0):.2f}%",
                    'Total Trades': str(metrics.get('total_trades', 0)),
                    'Win Rate': f"{metrics.get('win_rate', 0)*100:.1f}%",
                    'Profit Factor': f"{metrics.get('profit_factor', 0):.2f}"
                },
                'Risk Metrics': {
                    'Sharpe Ratio': f"{metrics.get('sharpe_ratio', 0):.2f}",
                    'Sortino Ratio': f"{metrics.get('sortino_ratio', 0):.2f}",
                    'Max Drawdown': f"{metrics.get('max_drawdown', 0)*100:.1f}%",
                    'Volatility': f"{metrics.get('volatility', 0)*100:.1f}%"
                },
                'Trade Analysis': {
                    'Average Win': f"{metrics.get('average_win', 0)*100:.2f}%",
                    'Average Loss': f"{metrics.get('average_loss', 0)*100:.2f}%",
                    'Largest Win': f"{metrics.get('largest_win', 0)*100:.2f}%",
                    'Largest Loss': f"{metrics.get('largest_loss', 0)*100:.2f}%"
                }
            }
        }

        table_file = self.output_dir / f"metrics_table_{strategy_name.replace(' ', '_')}.json"
        with open(table_file, 'w') as f:
            json.dump(table_data, f, indent=2)

        return str(table_file)

    async def _create_monthly_returns_heatmap(self, results: Dict[str, Any], strategy_name: str) -> str:
        """Create monthly returns heatmap data"""
        trades = results.get('trades', [])
        if not trades:
            return ""

        # Simulate monthly returns (in real implementation, would use actual dates)
        monthly_data = {}
        current_month = 1
        monthly_return = 0
        trades_per_month = max(1, len(trades) // 12)  # Distribute trades across 12 months

        for i, trade in enumerate(trades):
            monthly_return += trade.get('pnl_pct', 0)

            if (i + 1) % trades_per_month == 0 or i == len(trades) - 1:
                month_name = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][current_month - 1]
                monthly_data[month_name] = monthly_return
                monthly_return = 0
                current_month = min(12, current_month + 1)

        heatmap_data = {
            'title': f'Monthly Returns Heatmap - {strategy_name}',
            'data': monthly_data,
            'chart_type': 'heatmap',
            'description': 'Monthly performance breakdown',
            'best_month': max(monthly_data.keys(), key=lambda x: monthly_data[x]) if monthly_data else None,
            'worst_month': min(monthly_data.keys(), key=lambda x: monthly_data[x]) if monthly_data else None
        }

        heatmap_file = self.output_dir / f"monthly_returns_{strategy_name.replace(' ', '_')}.json"
        with open(heatmap_file, 'w') as f:
            json.dump(heatmap_data, f, indent=2)

        return str(heatmap_file)

    async def _create_trade_timeline_chart(self, results: Dict[str, Any],
                                         market_data: pl.DataFrame, strategy_name: str) -> str:
        """Create trade timeline visualization"""
        trades = results.get('trades', [])
        if not trades:
            return ""

        # Create timeline data
        timeline_data = []

        for i, trade in enumerate(trades):
            trade_info = {
                'trade_id': i + 1,
                'entry_time': f"Day_{i*2}",  # Simplified timing
                'exit_time': f"Day_{i*2+1}",
                'pnl_pct': trade.get('pnl_pct', 0),
                'trade_type': 'Long' if trade.get('pnl_pct', 0) > 0 else 'Short',  # Simplified
                'duration': 1,  # Simplified duration
                'entry_price': trade.get('entry_price', 100),
                'exit_price': trade.get('exit_price', 100)
            }
            timeline_data.append(trade_info)

        chart_data = {
            'title': f'Trade Timeline - {strategy_name}',
            'trades': timeline_data,
            'chart_type': 'timeline',
            'description': f'Timeline of {len(trades)} trades with entry/exit points',
            'summary': {
                'total_trades': len(trades),
                'profitable_trades': sum(1 for t in timeline_data if t['pnl_pct'] > 0),
                'avg_duration': np.mean([t['duration'] for t in timeline_data])
            }
        }

        timeline_file = self.output_dir / f"trade_timeline_{strategy_name.replace(' ', '_')}.json"
        with open(timeline_file, 'w') as f:
            json.dump(chart_data, f, indent=2)

        return str(timeline_file)

    async def _create_risk_metrics_chart(self, results: Dict[str, Any], strategy_name: str) -> str:
        """Create risk metrics visualization"""
        metrics = results.get('metrics', {})

        # Risk metrics radar chart data
        risk_metrics = {
            'Sharpe Ratio': min(metrics.get('sharpe_ratio', 0), 3),  # Cap at 3 for visualization
            'Sortino Ratio': min(metrics.get('sortino_ratio', 0), 3),
            'Calmar Ratio': min(metrics.get('calmar_ratio', 0), 3),
            'Win Rate': metrics.get('win_rate', 0),
            'Profit Factor': min(metrics.get('profit_factor', 0), 5),  # Cap at 5
            'Recovery Factor': min(metrics.get('recovery_factor', 0), 3)
        }

        # Normalize values for radar chart (0-1 scale)
        normalized_metrics = {}
        for metric, value in risk_metrics.items():
            if metric == 'Win Rate':
                normalized_metrics[metric] = value  # Already 0-1
            elif metric in ['Sharpe Ratio', 'Sortino Ratio', 'Calmar Ratio', 'Recovery Factor']:
                normalized_metrics[metric] = max(0, min(value / 3, 1))  # Normalize to 0-1
            elif metric == 'Profit Factor':
                normalized_metrics[metric] = max(0, min((value - 1) / 4, 1))  # 1-5 -> 0-1
            else:
                normalized_metrics[metric] = max(0, min(value, 1))

        chart_data = {
            'title': f'Risk Metrics Radar - {strategy_name}',
            'metrics': normalized_metrics,
            'raw_metrics': risk_metrics,
            'chart_type': 'radar',
            'description': 'Comprehensive risk assessment across multiple dimensions'
        }

        risk_file = self.output_dir / f"risk_metrics_{strategy_name.replace(' ', '_')}.json"
        with open(risk_file, 'w') as f:
            json.dump(chart_data, f, indent=2)

        return str(risk_file)

    async def _create_html_dashboard(self, dashboard_files: Dict[str, str], strategy_name: str) -> str:
        """Create HTML dashboard combining all visualizations"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Backtest Dashboard - {strategy_name}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .chart-container {{ margin: 20px 0; padding: 20px; border: 1px solid #ddd; }}
        .metrics-table {{ width: 100%; border-collapse: collapse; }}
        .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .metrics-table th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Backtest Dashboard: {strategy_name}</h1>
        <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="chart-container">
        <h2>Performance Overview</h2>
        <p>This dashboard provides comprehensive analysis of the backtest results.</p>
        <ul>
            <li><strong>Equity Curve:</strong> {dashboard_files.get('equity_curve', 'Not available')}</li>
            <li><strong>Drawdown Analysis:</strong> {dashboard_files.get('drawdown', 'Not available')}</li>
            <li><strong>Trade Distribution:</strong> {dashboard_files.get('trade_distribution', 'Not available')}</li>
            <li><strong>Monthly Returns:</strong> {dashboard_files.get('monthly_returns', 'Not available')}</li>
            <li><strong>Trade Timeline:</strong> {dashboard_files.get('trade_timeline', 'Not available')}</li>
            <li><strong>Risk Metrics:</strong> {dashboard_files.get('risk_metrics', 'Not available')}</li>
        </ul>
    </div>

    <div class="chart-container">
        <h2>Key Insights</h2>
        <p>📊 All chart data has been saved as JSON files for further analysis.</p>
        <p>📈 Use the provided data files with your preferred charting library (Chart.js, D3.js, etc.)</p>
        <p>🔍 Each JSON file contains structured data ready for visualization.</p>
    </div>

    <div class="chart-container">
        <h2>Files Generated</h2>
        <ul>
"""

        for chart_type, file_path in dashboard_files.items():
            if chart_type != 'dashboard':
                html_content += f"            <li><strong>{chart_type.replace('_', ' ').title()}:</strong> {Path(file_path).name}</li>\n"

        html_content += """
        </ul>
    </div>
</body>
</html>
"""

        dashboard_file = self.output_dir / f"dashboard_{strategy_name.replace(' ', '_')}.html"
        with open(dashboard_file, 'w') as f:
            f.write(html_content)

        return str(dashboard_file)

    async def debug_strategy_signals(self, strategy: Dict[str, Any],
                                   market_data: pl.DataFrame,
                                   trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Debug strategy signals and trade execution"""
        logger.info("[DEBUG] Analyzing strategy signals...")

        debug_info = {
            'strategy_name': strategy.get('name', 'Unknown'),
            'total_data_points': len(market_data),
            'total_trades': len(trades),
            'signal_analysis': {},
            'execution_analysis': {},
            'potential_issues': []
        }

        # Analyze signal frequency
        if trades:
            avg_trades_per_period = len(trades) / len(market_data) * 100
            debug_info['signal_analysis']['trade_frequency'] = f"{avg_trades_per_period:.2f}% of periods"

            if avg_trades_per_period > 50:
                debug_info['potential_issues'].append("High trade frequency - possible overtrading")
            elif avg_trades_per_period < 1:
                debug_info['potential_issues'].append("Low trade frequency - signals may be too restrictive")

        # Analyze trade execution
        if trades:
            pnl_values = [t.get('pnl_pct', 0) for t in trades]
            win_rate = sum(1 for pnl in pnl_values if pnl > 0) / len(pnl_values)

            debug_info['execution_analysis'] = {
                'win_rate': f"{win_rate*100:.1f}%",
                'avg_trade_pnl': f"{np.mean(pnl_values):.2f}%",
                'best_trade': f"{max(pnl_values):.2f}%",
                'worst_trade': f"{min(pnl_values):.2f}%"
            }

            if win_rate < 0.3:
                debug_info['potential_issues'].append("Low win rate - review entry conditions")
            elif win_rate > 0.8:
                debug_info['potential_issues'].append("Very high win rate - check for look-ahead bias")

        # Save debug report
        debug_file = self.output_dir / f"debug_report_{strategy.get('name', 'unknown').replace(' ', '_')}.json"
        with open(debug_file, 'w') as f:
            json.dump(debug_info, f, indent=2)

        return debug_info

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 9] SIGNAL DEBUGGING & REPLAY
# ═══════════════════════════════════════════════════════════════════════════════

class SignalAnalyzer:
    """Advanced signal analysis and debugging system"""

    def __init__(self):
        self.signal_history = []
        self.replay_sessions = {}

    async def analyze_strategy_signals(self, strategy: Dict[str, Any],
                                     market_data: pl.DataFrame,
                                     detailed_analysis: bool = True) -> Dict[str, Any]:
        """Comprehensive signal analysis"""
        logger.info(f"[SIGNAL] Analyzing signals for {strategy.get('name', 'Unknown')}")

        # Generate signals for each data point
        signals = await self._generate_signal_series(strategy, market_data)

        # Analyze signal quality
        signal_quality = await self._assess_signal_quality(signals, market_data)

        # Analyze signal timing
        timing_analysis = await self._analyze_signal_timing(signals, market_data)

        # Analyze signal clustering
        clustering_analysis = await self._analyze_signal_clustering(signals)

        # Analyze signal-to-noise ratio
        noise_analysis = await self._analyze_signal_noise_ratio(signals, market_data)

        analysis_result = {
            'strategy_name': strategy.get('name', 'Unknown'),
            'total_signals': len([s for s in signals if s['signal'] != 'hold']),
            'signal_breakdown': self._count_signal_types(signals),
            'signal_quality': signal_quality,
            'timing_analysis': timing_analysis,
            'clustering_analysis': clustering_analysis,
            'noise_analysis': noise_analysis,
            'recommendations': self._generate_signal_recommendations(signals, signal_quality)
        }

        # Store for replay
        self.signal_history.append({
            'timestamp': datetime.now(),
            'strategy': strategy,
            'signals': signals,
            'analysis': analysis_result
        })

        return analysis_result

    async def _generate_signal_series(self, strategy: Dict[str, Any],
                                    market_data: pl.DataFrame) -> List[Dict[str, Any]]:
        """Generate signal for each data point"""
        signals = []

        # Add technical indicators needed for signal generation
        data_with_indicators = market_data.with_columns([
            pl.col("close").rolling_mean(window_size=20).alias("sma_20"),
            pl.col("close").rolling_mean(window_size=50).alias("sma_50"),
            ((pl.col("close") - pl.col("close").shift(1)) / pl.col("close").shift(1) * 100).alias("returns")
        ])

        for i in range(len(data_with_indicators)):
            row = data_with_indicators[i]

            # Simplified signal generation (in real implementation, would use actual strategy logic)
            signal_info = {
                'index': i,
                'timestamp': f"Period_{i}",
                'price': row['close'][0] if row['close'] else 0,
                'signal': 'hold',
                'signal_strength': 0.0,
                'conditions_met': [],
                'indicators': {
                    'sma_20': row['sma_20'][0] if row['sma_20'] and not row['sma_20'].is_null().any() else 0,
                    'sma_50': row['sma_50'][0] if row['sma_50'] and not row['sma_50'].is_null().any() else 0,
                    'returns': row['returns'][0] if row['returns'] and not row['returns'].is_null().any() else 0
                }
            }

            # Simple signal logic based on moving averages
            if signal_info['indicators']['sma_20'] > signal_info['indicators']['sma_50'] * 1.01:
                signal_info['signal'] = 'long'
                signal_info['signal_strength'] = 0.7
                signal_info['conditions_met'].append('SMA_20 > SMA_50')
            elif signal_info['indicators']['sma_20'] < signal_info['indicators']['sma_50'] * 0.99:
                signal_info['signal'] = 'short'
                signal_info['signal_strength'] = 0.7
                signal_info['conditions_met'].append('SMA_20 < SMA_50')

            signals.append(signal_info)

        return signals

    async def _assess_signal_quality(self, signals: List[Dict[str, Any]],
                                   market_data: pl.DataFrame) -> Dict[str, Any]:
        """Assess the quality of generated signals"""
        if not signals:
            return {}

        # Count signal types
        long_signals = [s for s in signals if s['signal'] == 'long']
        short_signals = [s for s in signals if s['signal'] == 'short']
        hold_signals = [s for s in signals if s['signal'] == 'hold']

        # Calculate signal strength distribution
        signal_strengths = [s['signal_strength'] for s in signals if s['signal'] != 'hold']

        # Analyze signal persistence (how long signals last)
        signal_persistence = self._calculate_signal_persistence(signals)

        # Calculate signal accuracy (simplified - would need forward-looking returns in real implementation)
        signal_accuracy = self._estimate_signal_accuracy(signals, market_data)

        return {
            'signal_distribution': {
                'long_signals': len(long_signals),
                'short_signals': len(short_signals),
                'hold_signals': len(hold_signals),
                'signal_ratio': len(long_signals) / len(short_signals) if short_signals else float('inf')
            },
            'signal_strength': {
                'average_strength': np.mean(signal_strengths) if signal_strengths else 0,
                'strength_std': np.std(signal_strengths) if len(signal_strengths) > 1 else 0,
                'strong_signals': sum(1 for s in signal_strengths if s > 0.8),
                'weak_signals': sum(1 for s in signal_strengths if s < 0.3)
            },
            'signal_persistence': signal_persistence,
            'estimated_accuracy': signal_accuracy
        }

    async def _analyze_signal_timing(self, signals: List[Dict[str, Any]],
                                   market_data: pl.DataFrame) -> Dict[str, Any]:
        """Analyze signal timing characteristics"""
        if not signals:
            return {}

        # Find signal transitions
        transitions = []
        prev_signal = 'hold'

        for i, signal_info in enumerate(signals):
            current_signal = signal_info['signal']
            if current_signal != prev_signal and current_signal != 'hold':
                transitions.append({
                    'index': i,
                    'from_signal': prev_signal,
                    'to_signal': current_signal,
                    'price': signal_info['price']
                })
            prev_signal = current_signal

        # Analyze transition frequency
        avg_time_between_signals = len(signals) / len(transitions) if transitions else 0

        # Analyze signal clustering
        signal_gaps = []
        for i in range(1, len(transitions)):
            gap = transitions[i]['index'] - transitions[i-1]['index']
            signal_gaps.append(gap)

        return {
            'total_transitions': len(transitions),
            'avg_time_between_signals': avg_time_between_signals,
            'signal_gaps': {
                'min_gap': min(signal_gaps) if signal_gaps else 0,
                'max_gap': max(signal_gaps) if signal_gaps else 0,
                'avg_gap': np.mean(signal_gaps) if signal_gaps else 0
            },
            'transitions': transitions[:10]  # First 10 transitions for inspection
        }

    async def _analyze_signal_clustering(self, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze signal clustering patterns"""
        if not signals:
            return {}

        # Find consecutive signal periods
        signal_runs = []
        current_run = {'signal': 'hold', 'start': 0, 'length': 0}

        for i, signal_info in enumerate(signals):
            if signal_info['signal'] != current_run['signal']:
                if current_run['length'] > 0:
                    signal_runs.append(current_run.copy())
                current_run = {
                    'signal': signal_info['signal'],
                    'start': i,
                    'length': 1
                }
            else:
                current_run['length'] += 1

        # Add final run
        if current_run['length'] > 0:
            signal_runs.append(current_run)

        # Analyze run lengths
        long_runs = [r for r in signal_runs if r['signal'] == 'long']
        short_runs = [r for r in signal_runs if r['signal'] == 'short']

        return {
            'total_runs': len(signal_runs),
            'long_runs': {
                'count': len(long_runs),
                'avg_length': np.mean([r['length'] for r in long_runs]) if long_runs else 0,
                'max_length': max([r['length'] for r in long_runs]) if long_runs else 0
            },
            'short_runs': {
                'count': len(short_runs),
                'avg_length': np.mean([r['length'] for r in short_runs]) if short_runs else 0,
                'max_length': max([r['length'] for r in short_runs]) if short_runs else 0
            },
            'clustering_score': self._calculate_clustering_score(signal_runs)
        }

    async def _analyze_signal_noise_ratio(self, signals: List[Dict[str, Any]],
                                        market_data: pl.DataFrame) -> Dict[str, Any]:
        """Analyze signal-to-noise ratio"""
        if not signals:
            return {}

        # Calculate signal consistency
        signal_changes = 0
        prev_signal = 'hold'

        for signal_info in signals:
            if signal_info['signal'] != prev_signal:
                signal_changes += 1
            prev_signal = signal_info['signal']

        # Calculate noise metrics
        total_signals = len([s for s in signals if s['signal'] != 'hold'])
        noise_ratio = signal_changes / len(signals) if signals else 0

        # Signal strength consistency
        signal_strengths = [s['signal_strength'] for s in signals if s['signal'] != 'hold']
        strength_consistency = 1 - (np.std(signal_strengths) / (np.mean(signal_strengths) + 1e-6)) if signal_strengths else 0

        return {
            'total_signal_changes': signal_changes,
            'noise_ratio': noise_ratio,
            'signal_consistency': max(0, 1 - noise_ratio),
            'strength_consistency': max(0, strength_consistency),
            'quality_score': (max(0, 1 - noise_ratio) + max(0, strength_consistency)) / 2
        }

    def _count_signal_types(self, signals: List[Dict[str, Any]]) -> Dict[str, int]:
        """Count different types of signals"""
        counts = {'long': 0, 'short': 0, 'hold': 0}

        for signal_info in signals:
            signal_type = signal_info['signal']
            if signal_type in counts:
                counts[signal_type] += 1

        return counts

    def _calculate_signal_persistence(self, signals: List[Dict[str, Any]]) -> Dict[str, float]:
        """Calculate how long signals persist"""
        if not signals:
            return {}

        signal_durations = {'long': [], 'short': []}
        current_signal = 'hold'
        current_duration = 0

        for signal_info in signals:
            if signal_info['signal'] == current_signal:
                current_duration += 1
            else:
                if current_signal in signal_durations and current_duration > 0:
                    signal_durations[current_signal].append(current_duration)
                current_signal = signal_info['signal']
                current_duration = 1

        # Add final duration
        if current_signal in signal_durations and current_duration > 0:
            signal_durations[current_signal].append(current_duration)

        return {
            'avg_long_duration': np.mean(signal_durations['long']) if signal_durations['long'] else 0,
            'avg_short_duration': np.mean(signal_durations['short']) if signal_durations['short'] else 0,
            'max_long_duration': max(signal_durations['long']) if signal_durations['long'] else 0,
            'max_short_duration': max(signal_durations['short']) if signal_durations['short'] else 0
        }

    def _estimate_signal_accuracy(self, signals: List[Dict[str, Any]],
                                market_data: pl.DataFrame) -> Dict[str, float]:
        """Estimate signal accuracy (simplified)"""
        if not signals or len(market_data) < 2:
            return {}

        # Simplified accuracy estimation using next-period returns
        correct_signals = 0
        total_signals = 0

        for i, signal_info in enumerate(signals[:-1]):  # Exclude last signal
            if signal_info['signal'] != 'hold' and i + 1 < len(market_data):
                current_price = market_data[i]['close'][0] if market_data[i]['close'] else 0
                next_price = market_data[i + 1]['close'][0] if market_data[i + 1]['close'] else 0

                if current_price > 0:
                    next_return = (next_price - current_price) / current_price

                    # Check if signal direction matches return direction
                    if ((signal_info['signal'] == 'long' and next_return > 0) or
                        (signal_info['signal'] == 'short' and next_return < 0)):
                        correct_signals += 1

                    total_signals += 1

        accuracy = correct_signals / total_signals if total_signals > 0 else 0

        return {
            'estimated_accuracy': accuracy,
            'correct_signals': correct_signals,
            'total_evaluated': total_signals
        }

    def _calculate_clustering_score(self, signal_runs: List[Dict[str, Any]]) -> float:
        """Calculate signal clustering score"""
        if not signal_runs:
            return 0.0

        # Calculate coefficient of variation for run lengths
        run_lengths = [r['length'] for r in signal_runs if r['signal'] != 'hold']

        if len(run_lengths) < 2:
            return 0.0

        mean_length = np.mean(run_lengths)
        std_length = np.std(run_lengths)

        # Lower coefficient of variation indicates more consistent clustering
        cv = std_length / (mean_length + 1e-6)
        clustering_score = max(0, 1 - cv)

        return clustering_score

    def _generate_signal_recommendations(self, signals: List[Dict[str, Any]],
                                       signal_quality: Dict[str, Any]) -> List[str]:
        """Generate recommendations for signal improvement"""
        recommendations = []

        if not signals:
            return ["No signals generated - check strategy conditions"]

        # Check signal frequency
        total_signals = len([s for s in signals if s['signal'] != 'hold'])
        signal_frequency = total_signals / len(signals)

        if signal_frequency > 0.5:
            recommendations.append("High signal frequency detected - consider tightening entry conditions")
        elif signal_frequency < 0.05:
            recommendations.append("Low signal frequency detected - consider relaxing entry conditions")

        # Check signal balance
        signal_breakdown = self._count_signal_types(signals)
        long_short_ratio = signal_breakdown['long'] / (signal_breakdown['short'] + 1e-6)

        if long_short_ratio > 3:
            recommendations.append("Strategy heavily biased toward long signals - consider adding short conditions")
        elif long_short_ratio < 0.33:
            recommendations.append("Strategy heavily biased toward short signals - consider adding long conditions")

        # Check signal strength
        avg_strength = signal_quality.get('signal_strength', {}).get('average_strength', 0)
        if avg_strength < 0.5:
            recommendations.append("Low average signal strength - consider improving signal confidence calculation")

        # Check noise ratio
        noise_analysis = signal_quality.get('noise_analysis', {})
        if noise_analysis.get('noise_ratio', 0) > 0.3:
            recommendations.append("High signal noise detected - consider adding signal filtering or smoothing")

        if not recommendations:
            recommendations.append("Signal quality appears good - no major issues detected")

        return recommendations

    async def replay_trading_session(self, session_id: str,
                                   start_index: int = 0,
                                   end_index: int = None) -> Dict[str, Any]:
        """Replay a trading session step by step"""
        if session_id not in self.replay_sessions:
            return {'error': f'Session {session_id} not found'}

        session = self.replay_sessions[session_id]
        signals = session['signals']

        if end_index is None:
            end_index = len(signals)

        replay_data = {
            'session_id': session_id,
            'replay_range': f"{start_index} to {end_index}",
            'steps': [],
            'summary': {}
        }

        # Replay each step
        for i in range(start_index, min(end_index, len(signals))):
            signal_info = signals[i]

            step_data = {
                'step': i,
                'timestamp': signal_info['timestamp'],
                'price': signal_info['price'],
                'signal': signal_info['signal'],
                'signal_strength': signal_info['signal_strength'],
                'conditions_met': signal_info['conditions_met'],
                'indicators': signal_info['indicators']
            }

            replay_data['steps'].append(step_data)

        # Generate replay summary
        replayed_signals = [step['signal'] for step in replay_data['steps']]
        replay_data['summary'] = {
            'total_steps': len(replay_data['steps']),
            'signals_generated': len([s for s in replayed_signals if s != 'hold']),
            'long_signals': replayed_signals.count('long'),
            'short_signals': replayed_signals.count('short'),
            'hold_periods': replayed_signals.count('hold')
        }

        return replay_data

    async def create_replay_session(self, strategy: Dict[str, Any],
                                  market_data: pl.DataFrame) -> str:
        """Create a new replay session"""
        session_id = f"replay_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.replay_sessions)}"

        # Generate signals for replay
        signals = await self._generate_signal_series(strategy, market_data)

        self.replay_sessions[session_id] = {
            'created_at': datetime.now(),
            'strategy': strategy,
            'market_data_length': len(market_data),
            'signals': signals,
            'total_signals': len([s for s in signals if s['signal'] != 'hold'])
        }

        logger.info(f"[REPLAY] Created session {session_id} with {len(signals)} data points")
        return session_id

# ═══════════════════════════════════════════════════════════════════════════════
# [FEATURE 10] LLM-EXPLAINABLE RESULTS SUMMARY
# ═══════════════════════════════════════════════════════════════════════════════

class LLMResultsExplainer:
    """AI-powered results interpretation and explanation system"""

    def __init__(self):
        self.explanation_templates = self._load_explanation_templates()
        self.insight_patterns = self._load_insight_patterns()

    async def generate_comprehensive_explanation(self, backtest_results: Dict[str, Any],
                                               strategy: Dict[str, Any],
                                               market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate comprehensive AI-powered explanation of backtest results"""
        logger.info(f"[LLM] Generating explanation for {strategy.get('name', 'Unknown')}")

        # Extract key metrics and data
        metrics = backtest_results.get('metrics', {})
        trades = backtest_results.get('trades', [])

        # Generate different types of explanations
        performance_explanation = await self._explain_performance_metrics(metrics, trades)
        risk_explanation = await self._explain_risk_characteristics(metrics, trades)
        strategy_explanation = await self._explain_strategy_behavior(strategy, trades)
        market_explanation = await self._explain_market_context(trades, market_context)

        # Generate actionable insights
        insights = await self._generate_actionable_insights(metrics, trades, strategy)

        # Generate recommendations
        recommendations = await self._generate_strategy_recommendations(metrics, trades, strategy)

        # Create executive summary
        executive_summary = await self._create_executive_summary(
            performance_explanation, risk_explanation, insights, recommendations
        )

        comprehensive_explanation = {
            'strategy_name': strategy.get('name', 'Unknown'),
            'generated_at': datetime.now().isoformat(),
            'executive_summary': executive_summary,
            'detailed_analysis': {
                'performance': performance_explanation,
                'risk_profile': risk_explanation,
                'strategy_behavior': strategy_explanation,
                'market_context': market_explanation
            },
            'key_insights': insights,
            'recommendations': recommendations,
            'confidence_score': self._calculate_explanation_confidence(metrics, trades)
        }

        return comprehensive_explanation

    async def _explain_performance_metrics(self, metrics: Dict[str, Any],
                                         trades: List[Dict[str, Any]]) -> Dict[str, str]:
        """Generate natural language explanation of performance metrics"""
        total_return = metrics.get('total_return', 0)
        win_rate = metrics.get('win_rate', 0)
        sharpe_ratio = metrics.get('sharpe_ratio', 0)
        profit_factor = metrics.get('profit_factor', 0)

        explanations = {}

        # Total Return Explanation
        if total_return > 20:
            explanations['total_return'] = f"Excellent performance with {total_return:.1f}% total return, significantly outperforming typical market returns."
        elif total_return > 10:
            explanations['total_return'] = f"Strong performance with {total_return:.1f}% total return, demonstrating solid profitability."
        elif total_return > 0:
            explanations['total_return'] = f"Modest positive performance with {total_return:.1f}% total return, showing consistent but limited gains."
        else:
            explanations['total_return'] = f"Negative performance with {total_return:.1f}% total return, indicating strategy needs significant improvement."

        # Win Rate Explanation
        if win_rate > 0.6:
            explanations['win_rate'] = f"High win rate of {win_rate*100:.1f}% indicates the strategy is very good at picking winning trades."
        elif win_rate > 0.4:
            explanations['win_rate'] = f"Balanced win rate of {win_rate*100:.1f}% suggests a reasonable trade selection process."
        else:
            explanations['win_rate'] = f"Low win rate of {win_rate*100:.1f}% indicates many losing trades, but may be compensated by larger wins."

        # Sharpe Ratio Explanation
        if sharpe_ratio > 2:
            explanations['sharpe_ratio'] = f"Exceptional risk-adjusted returns with Sharpe ratio of {sharpe_ratio:.2f}, indicating excellent risk management."
        elif sharpe_ratio > 1:
            explanations['sharpe_ratio'] = f"Good risk-adjusted returns with Sharpe ratio of {sharpe_ratio:.2f}, showing solid risk-return balance."
        elif sharpe_ratio > 0:
            explanations['sharpe_ratio'] = f"Positive but modest risk-adjusted returns with Sharpe ratio of {sharpe_ratio:.2f}."
        else:
            explanations['sharpe_ratio'] = f"Poor risk-adjusted returns with Sharpe ratio of {sharpe_ratio:.2f}, indicating excessive risk for the returns achieved."

        # Profit Factor Explanation
        if profit_factor > 2:
            explanations['profit_factor'] = f"Excellent profit factor of {profit_factor:.2f} shows winning trades significantly outweigh losing trades."
        elif profit_factor > 1.5:
            explanations['profit_factor'] = f"Good profit factor of {profit_factor:.2f} indicates healthy profit generation relative to losses."
        elif profit_factor > 1:
            explanations['profit_factor'] = f"Modest profit factor of {profit_factor:.2f} shows slight edge but room for improvement."
        else:
            explanations['profit_factor'] = f"Poor profit factor of {profit_factor:.2f} indicates losses exceed profits, requiring strategy revision."

        return explanations

    async def _explain_risk_characteristics(self, metrics: Dict[str, Any],
                                          trades: List[Dict[str, Any]]) -> Dict[str, str]:
        """Generate natural language explanation of risk characteristics"""
        max_drawdown = metrics.get('max_drawdown', 0)
        volatility = metrics.get('volatility', 0)
        var_95 = metrics.get('var_95', 0)

        explanations = {}

        # Maximum Drawdown Explanation
        if max_drawdown < 0.05:
            explanations['max_drawdown'] = f"Very low maximum drawdown of {max_drawdown*100:.1f}% indicates excellent capital preservation."
        elif max_drawdown < 0.15:
            explanations['max_drawdown'] = f"Moderate maximum drawdown of {max_drawdown*100:.1f}% shows reasonable risk control."
        elif max_drawdown < 0.30:
            explanations['max_drawdown'] = f"Significant maximum drawdown of {max_drawdown*100:.1f}% suggests periods of substantial losses."
        else:
            explanations['max_drawdown'] = f"High maximum drawdown of {max_drawdown*100:.1f}% indicates poor risk management and large potential losses."

        # Volatility Explanation
        if volatility < 0.10:
            explanations['volatility'] = f"Low volatility of {volatility*100:.1f}% indicates stable, consistent returns."
        elif volatility < 0.20:
            explanations['volatility'] = f"Moderate volatility of {volatility*100:.1f}% shows balanced risk-return characteristics."
        else:
            explanations['volatility'] = f"High volatility of {volatility*100:.1f}% indicates significant return variability and higher risk."

        # VaR Explanation
        if var_95:
            explanations['var_95'] = f"95% Value at Risk of {var_95*100:.1f}% means there's a 5% chance of losing more than this amount in a single period."

        return explanations

    async def _explain_strategy_behavior(self, strategy: Dict[str, Any],
                                       trades: List[Dict[str, Any]]) -> Dict[str, str]:
        """Generate natural language explanation of strategy behavior"""
        strategy_name = strategy.get('name', 'Unknown')

        explanations = {}

        # Trading Frequency Analysis
        if trades:
            avg_trade_duration = 1  # Simplified - would calculate actual duration

            if len(trades) > 100:
                explanations['frequency'] = f"High-frequency strategy with {len(trades)} trades, indicating active trading approach."
            elif len(trades) > 20:
                explanations['frequency'] = f"Moderate trading frequency with {len(trades)} trades, showing selective trade entry."
            else:
                explanations['frequency'] = f"Low trading frequency with {len(trades)} trades, indicating very selective or long-term approach."

        # Strategy Type Analysis
        if 'momentum' in strategy_name.lower():
            explanations['type'] = "Momentum-based strategy that aims to capture trending price movements."
        elif 'mean reversion' in strategy_name.lower() or 'reversal' in strategy_name.lower():
            explanations['type'] = "Mean reversion strategy that profits from price corrections back to average levels."
        elif 'scalping' in strategy_name.lower():
            explanations['type'] = "Scalping strategy designed for quick, small profits from short-term price movements."
        else:
            explanations['type'] = "Strategy behavior analysis based on observed trading patterns."

        return explanations

    async def _explain_market_context(self, trades: List[Dict[str, Any]],
                                    market_context: Dict[str, Any] = None) -> Dict[str, str]:
        """Generate natural language explanation of market context"""
        explanations = {}

        if market_context:
            market_regime = market_context.get('regime', 'unknown')
            volatility_regime = market_context.get('volatility', 'normal')

            explanations['regime'] = f"Strategy performed during {market_regime} market conditions with {volatility_regime} volatility."
        else:
            # Analyze from trade patterns
            if trades:
                pnl_values = [t.get('pnl_pct', 0) for t in trades]
                volatility = np.std(pnl_values) if len(pnl_values) > 1 else 0

                if volatility > 5:
                    explanations['inferred_conditions'] = "High trade volatility suggests challenging or volatile market conditions."
                elif volatility < 1:
                    explanations['inferred_conditions'] = "Low trade volatility suggests stable market conditions."
                else:
                    explanations['inferred_conditions'] = "Moderate trade volatility suggests normal market conditions."

        return explanations

    async def _generate_actionable_insights(self, metrics: Dict[str, Any],
                                          trades: List[Dict[str, Any]],
                                          strategy: Dict[str, Any]) -> List[str]:
        """Generate actionable insights from the analysis"""
        insights = []

        total_return = metrics.get('total_return', 0)
        win_rate = metrics.get('win_rate', 0)
        sharpe_ratio = metrics.get('sharpe_ratio', 0)
        max_drawdown = metrics.get('max_drawdown', 0)

        # Performance insights
        if total_return > 15 and sharpe_ratio > 1.5:
            insights.append("🎯 Strong performance with excellent risk-adjusted returns - consider increasing position sizes")
        elif total_return > 0 and sharpe_ratio < 0.5:
            insights.append("⚠️ Positive returns but poor risk adjustment - focus on reducing volatility")

        # Risk insights
        if max_drawdown > 0.25:
            insights.append("🚨 High drawdown risk detected - implement stricter stop-losses or position sizing")
        elif max_drawdown < 0.05:
            insights.append("✅ Excellent risk control - drawdown management is working well")

        # Win rate insights
        if win_rate > 0.7 and metrics.get('profit_factor', 0) < 1.5:
            insights.append("🔍 High win rate but low profit factor - winning trades may be too small")
        elif win_rate < 0.4 and metrics.get('profit_factor', 0) > 2:
            insights.append("💡 Low win rate but high profit factor - strategy captures large moves well")

        # Trading frequency insights
        if trades and len(trades) > 200:
            insights.append("⚡ High trading frequency - monitor transaction costs and slippage impact")
        elif trades and len(trades) < 10:
            insights.append("🐌 Low trading frequency - consider more opportunities or relaxed entry criteria")

        return insights

    async def _generate_strategy_recommendations(self, metrics: Dict[str, Any],
                                               trades: List[Dict[str, Any]],
                                               strategy: Dict[str, Any]) -> List[str]:
        """Generate specific strategy improvement recommendations"""
        recommendations = []

        total_return = metrics.get('total_return', 0)
        win_rate = metrics.get('win_rate', 0)
        sharpe_ratio = metrics.get('sharpe_ratio', 0)
        max_drawdown = metrics.get('max_drawdown', 0)

        # Performance-based recommendations
        if total_return < 5:
            recommendations.append("📈 Consider optimizing entry/exit criteria to improve overall returns")

        if sharpe_ratio < 1:
            recommendations.append("⚖️ Improve risk-adjusted returns by reducing position sizes during volatile periods")

        # Risk-based recommendations
        if max_drawdown > 0.20:
            recommendations.append("🛡️ Implement dynamic position sizing based on recent volatility")
            recommendations.append("🔄 Add portfolio heat rules to reduce exposure during losing streaks")

        # Win rate recommendations
        if win_rate < 0.35:
            recommendations.append("🎯 Review entry criteria - consider additional confirmation signals")
        elif win_rate > 0.75:
            recommendations.append("💰 High win rate suggests room to increase risk per trade for higher returns")

        # Strategy-specific recommendations
        strategy_name = strategy.get('name', '').lower()
        if 'momentum' in strategy_name and win_rate < 0.45:
            recommendations.append("🚀 Momentum strategies should have higher win rates - check trend identification")
        elif 'reversal' in strategy_name and max_drawdown > 0.15:
            recommendations.append("🔄 Mean reversion strategies need tight risk control - reduce position sizes")

        return recommendations

    async def _create_executive_summary(self, performance_explanation: Dict[str, str],
                                      risk_explanation: Dict[str, str],
                                      insights: List[str],
                                      recommendations: List[str]) -> str:
        """Create executive summary of the analysis"""
        summary_parts = []

        # Performance summary
        if 'total_return' in performance_explanation:
            summary_parts.append(performance_explanation['total_return'])

        # Risk summary
        if 'max_drawdown' in risk_explanation:
            summary_parts.append(risk_explanation['max_drawdown'])

        # Key insight
        if insights:
            summary_parts.append(f"Key insight: {insights[0].replace('🎯 ', '').replace('⚠️ ', '').replace('🚨 ', '')}")

        # Top recommendation
        if recommendations:
            summary_parts.append(f"Primary recommendation: {recommendations[0].replace('📈 ', '').replace('⚖️ ', '').replace('🛡️ ', '')}")

        return " ".join(summary_parts)

    def _calculate_explanation_confidence(self, metrics: Dict[str, Any],
                                        trades: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for the explanation"""
        confidence_factors = []

        # Data sufficiency
        if trades and len(trades) > 30:
            confidence_factors.append(0.9)
        elif trades and len(trades) > 10:
            confidence_factors.append(0.7)
        else:
            confidence_factors.append(0.4)

        # Metric completeness
        required_metrics = ['total_return', 'win_rate', 'sharpe_ratio', 'max_drawdown']
        available_metrics = sum(1 for metric in required_metrics if metric in metrics)
        metric_completeness = available_metrics / len(required_metrics)
        confidence_factors.append(metric_completeness)

        # Performance consistency
        if metrics.get('sharpe_ratio', 0) > 0.5:
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.6)

        return np.mean(confidence_factors)

    def _load_explanation_templates(self) -> Dict[str, str]:
        """Load explanation templates for different scenarios"""
        return {
            'high_performance': "This strategy demonstrates exceptional performance with strong risk-adjusted returns.",
            'moderate_performance': "This strategy shows solid performance with room for optimization.",
            'poor_performance': "This strategy requires significant improvement to achieve profitability.",
            'high_risk': "This strategy exhibits high risk characteristics that need attention.",
            'low_risk': "This strategy demonstrates excellent risk management."
        }

    def _load_insight_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for generating insights"""
        return {
            'momentum_patterns': [
                "Strong trending periods show best performance",
                "Choppy markets reduce strategy effectiveness",
                "Early trend identification is crucial"
            ],
            'mean_reversion_patterns': [
                "Oversold/overbought conditions provide best entries",
                "Strong trends can cause extended losses",
                "Quick profit-taking improves results"
            ],
            'general_patterns': [
                "Risk management is key to long-term success",
                "Market regime awareness improves performance",
                "Position sizing affects overall results"
            ]
        }

    async def generate_strategy_comparison(self, results_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comparative analysis of multiple strategies"""
        if len(results_list) < 2:
            return {'error': 'Need at least 2 strategies for comparison'}

        comparison = {
            'strategies_compared': len(results_list),
            'comparative_analysis': {},
            'ranking': {},
            'insights': []
        }

        # Extract metrics for comparison
        strategy_metrics = []
        for result in results_list:
            strategy_name = result.get('strategy_name', 'Unknown')
            metrics = result.get('detailed_analysis', {}).get('performance', {})

            strategy_metrics.append({
                'name': strategy_name,
                'total_return': float(result.get('metrics', {}).get('total_return', 0)),
                'sharpe_ratio': float(result.get('metrics', {}).get('sharpe_ratio', 0)),
                'max_drawdown': float(result.get('metrics', {}).get('max_drawdown', 0)),
                'win_rate': float(result.get('metrics', {}).get('win_rate', 0))
            })

        # Rank strategies
        comparison['ranking']['by_return'] = sorted(strategy_metrics, key=lambda x: x['total_return'], reverse=True)
        comparison['ranking']['by_sharpe'] = sorted(strategy_metrics, key=lambda x: x['sharpe_ratio'], reverse=True)
        comparison['ranking']['by_risk'] = sorted(strategy_metrics, key=lambda x: x['max_drawdown'])

        # Generate comparative insights
        best_return = comparison['ranking']['by_return'][0]
        best_sharpe = comparison['ranking']['by_sharpe'][0]
        lowest_risk = comparison['ranking']['by_risk'][0]

        comparison['insights'] = [
            f"🏆 Best overall returns: {best_return['name']} with {best_return['total_return']:.1f}%",
            f"⚖️ Best risk-adjusted returns: {best_sharpe['name']} with Sharpe ratio of {best_sharpe['sharpe_ratio']:.2f}",
            f"🛡️ Lowest risk: {lowest_risk['name']} with {lowest_risk['max_drawdown']*100:.1f}% max drawdown"
        ]

        return comparison

async def main_async():
    """Main async execution function"""
    logger.info("[INIT] Starting Enhanced Backtesting System with Individual Symbol Processing")
    logger.info("=" * 80)

    # Initial memory cleanup
    aggressive_memory_cleanup()

    # Initialize process pool if using multiprocessing
    if USE_MULTIPROCESSING:
        init_process_pool()

    # Log configuration
    logger.info(f"[LIST] Strategies file: {STRATEGIES_FILE}")
    logger.info(f"[STATUS] Output directory: {OUTPUT_DIR}")
    logger.info(f"💾 Compression: {COMPRESSION}")
    logger.info(f"[FAST] Concurrent strategies: {CONCURRENT_STRATEGIES}")
    logger.info(f"[CONFIG] CPU cores: {CPU_COUNT}")
    logger.info(f"[CONFIG] Process pool size: {PROCESS_POOL_SIZE}")
    logger.info(f"[CONFIG] Multiprocessing: {'Enabled' if USE_MULTIPROCESSING else 'Disabled'}")
    logger.info(f"[CONFIG] GPU acceleration: {'Available' if GPU_AVAILABLE else 'Not available'}")

    # Load strategies
    strategies = load_strategies()
    if not strategies:
        logger.error("[ERROR] No strategies loaded, exiting")
        return

    # Get available feature files
    feature_files = get_available_feature_files()
    if not feature_files:
        logger.error("[ERROR] No feature files found, exiting")
        return

    logger.info(f"[UPTIME] Will process {len(feature_files)} individual symbol files")

    # Process each symbol file individually
    start_time = time.time()
    total_files = len(feature_files)

    for idx, (file_path, symbol, timeframe) in enumerate(feature_files):
        logger.info(f"[PROGRESS] Processing file {idx+1}/{total_files}: {symbol} ({timeframe})")

        # Process single symbol file
        results = await process_symbol_file_async(file_path, symbol, timeframe, strategies)

        # Write results immediately for this symbol
        if results:
            await write_symbol_results_async(results, symbol, timeframe)
        else:
            logger.warning(f"[WARN] No results generated for {symbol}")

        # Memory cleanup between files
        del results
        aggressive_memory_cleanup()
        reset_polars_state()

        # Progress update
        progress = ((idx + 1) / total_files) * 100
        logger.info(f"Progress: {progress:.1f}% ({idx+1}/{total_files} files)")

    # Final summary
    end_time = time.time()
    total_time = end_time - start_time

    logger.info("🎉 ALL BACKTESTING COMPLETED SUCCESSFULLY!")
    logger.info(f"⏱️ Total processing time: {total_time:.1f} seconds")
    logger.info(f"[METRICS] Files processed: {total_files}")

    # Summary of output files
    output_files = list(Path(OUTPUT_DIR).glob("backtest_*.parquet"))
    if output_files:
        total_size = sum(f.stat().st_size for f in output_files) / (1024 * 1024)
        logger.info(f"[SUCCESS] Generated {len(output_files)} output files")
        logger.info(f"[FOLDER] Total output size: {total_size:.1f} MB")

        # Sample output files
        logger.info("[SAMPLE] Output files:")
        for i, output_file in enumerate(output_files[:5]):  # Show first 5
            file_size = output_file.stat().st_size / (1024 * 1024)
            logger.info(f"  - {output_file.name}: {file_size:.1f} MB")
        if len(output_files) > 5:
            logger.info(f"  ... and {len(output_files) - 5} more files")

    # Cleanup process pool
    if USE_MULTIPROCESSING:
        cleanup_process_pool()

async def process_symbol_file_async(file_path: str, symbol: str, timeframe: str, strategies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process a single symbol file asynchronously"""
    try:
        # Load symbol data
        logger.info(f"[LOAD] Loading data for {symbol} from {file_path}")
        symbol_df = pl.read_parquet(file_path)

        if len(symbol_df) < 20:
            logger.warning(f"[SKIP] Insufficient data for {symbol}: {len(symbol_df)} rows")
            return []

        # Process all strategies for this symbol
        all_results = []

        # Use asyncio for concurrent strategy processing
        semaphore = asyncio.Semaphore(CONCURRENT_STRATEGIES)

        async def process_strategy_async(strategy):
            async with semaphore:
                strategy_results = []
                for rr in RISK_REWARD_RATIOS:
                    result = await backtest_strategy_rr_async(symbol_df, strategy, timeframe, symbol, rr)
                    if result:
                        strategy_results.append(result)
                return strategy_results

        # Process strategies concurrently
        tasks = [process_strategy_async(strategy) for strategy in strategies]
        strategy_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Flatten results
        for result_list in strategy_results:
            if isinstance(result_list, list):
                all_results.extend(result_list)

        logger.info(f"[SUCCESS] Generated {len(all_results)} results for {symbol}")
        return all_results

    except Exception as e:
        logger.error(f"[ERROR] Failed to process {symbol}: {e}")
        return []

async def backtest_strategy_rr_async(symbol_df: pl.DataFrame, strategy: Dict[str, Any],
                                   timeframe: str, symbol: str, rr: List[float]) -> Optional[Dict[str, Any]]:
    """Async version of strategy backtesting"""
    try:
        # Run trade simulation
        trades = await simulate_trades_vectorized(symbol_df, strategy, rr, timeframe)

        if not trades:
            return None

        # Calculate performance metrics
        result = calculate_performance_metrics(trades, symbol, strategy['name'], timeframe, rr)
        return result

    except Exception as e:
        logger.error(f"Async backtest strategy R:R failed: {e}")
        return None

async def write_symbol_results_async(results: List[Dict[str, Any]], symbol: str, timeframe: str):
    """Write results for a single symbol to parquet file"""
    try:
        if not results:
            return

        # Create output filename
        output_filename = generate_output_filename(symbol, timeframe)
        output_path = Path(OUTPUT_DIR) / output_filename

        # Convert results to polars DataFrame
        results_df = pl.DataFrame(results)

        # Write to parquet with compression
        with _file_write_lock:
            results_df.write_parquet(
                output_path,
                compression=COMPRESSION,
                use_pyarrow=True
            )

        file_size = output_path.stat().st_size / (1024 * 1024)
        logger.info(f"[WRITE] Saved {len(results)} results for {symbol} to {output_filename} ({file_size:.1f} MB)")

    except Exception as e:
        logger.error(f"[ERROR] Error writing results for {symbol}: {e}")
    finally:
        # Force cleanup
        gc.collect()

async def main_async():
    """Main async execution function"""
    logger.info("[INIT] Starting Enhanced Backtesting System with Individual Symbol Processing")
    logger.info("=" * 80)

    # Initial memory cleanup
    aggressive_memory_cleanup()
    reset_polars_state()

    # Initialize process pool for CPU-intensive tasks
    if USE_MULTIPROCESSING:
        init_process_pool()

    # Configuration summary
    logger.info(f"[FOLDER] Data directory: {DATA_DIR}")
    logger.info(f"[LIST] Strategies file: {STRATEGIES_FILE}")
    logger.info(f"[STATUS] Output directory: {OUTPUT_DIR}")
    logger.info(f"💾 Compression: {COMPRESSION}")
    logger.info(f"[FAST] Concurrent strategies: {CONCURRENT_STRATEGIES}")
    logger.info(f"[CONFIG] CPU cores: {CPU_COUNT}")
    logger.info(f"[CONFIG] Process pool size: {PROCESS_POOL_SIZE}")
    logger.info(f"[CONFIG] Multiprocessing: {'Enabled' if USE_MULTIPROCESSING else 'Disabled'}")
    logger.info(f"[CONFIG] GPU acceleration: {'Available' if GPU_AVAILABLE else 'Not available'}")

    # Load strategies
    strategies = load_strategies()
    if not strategies:
        logger.error("[ERROR] No strategies loaded, exiting")
        return

    # Get available feature files
    feature_files = get_available_feature_files()
    if not feature_files:
        logger.error("[ERROR] No feature files found, exiting")
        return

    logger.info(f"[UPTIME] Will process {len(feature_files)} individual symbol files")

    # Process each symbol file individually
    start_time = time.time()
    total_files = len(feature_files)

    for idx, (file_path, symbol, timeframe) in enumerate(feature_files):
        logger.info(f"[PROGRESS] Processing file {idx+1}/{total_files}: {symbol} ({timeframe})")

        # Process single symbol file
        results = await process_symbol_file_async(file_path, symbol, timeframe, strategies)

        # Write results immediately for this symbol
        if results:
            await write_symbol_results_async(results, symbol, timeframe)
        else:
            logger.warning(f"[WARN] No results generated for {symbol}")

        # Memory cleanup between files
        del results
        aggressive_memory_cleanup()
        reset_polars_state()

        # Progress update
        progress = ((idx + 1) / total_files) * 100
        logger.info(f"Progress: {progress:.1f}% ({idx+1}/{total_files} files)")

    # Final summary
    end_time = time.time()
    total_time = end_time - start_time

    logger.info("🎉 ALL BACKTESTING COMPLETED SUCCESSFULLY!")
    logger.info(f"⏱️ Total processing time: {total_time:.1f} seconds")
    logger.info(f"[METRICS] Files processed: {total_files}")

    # Summary of output files
    output_files = list(Path(OUTPUT_DIR).glob("backtest_*.parquet"))
    if output_files:
        total_size = sum(f.stat().st_size for f in output_files) / (1024 * 1024)
        logger.info(f"[SUCCESS] Generated {len(output_files)} output files")
        logger.info(f"[FOLDER] Total output size: {total_size:.1f} MB")

        # Sample output files
        logger.info("[SAMPLE] Output files:")
        for i, output_file in enumerate(output_files[:5]):  # Show first 5
            file_size = output_file.stat().st_size / (1024 * 1024)
            logger.info(f"  - {output_file.name}: {file_size:.1f} MB")
        if len(output_files) > 5:
            logger.info(f"  ... and {len(output_files) - 5} more files")

    # Cleanup process pool
    if USE_MULTIPROCESSING:
        cleanup_process_pool()

async def process_symbol_file_async(file_path: str, symbol: str, timeframe: str, strategies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process a single symbol file asynchronously"""
    try:
        # Load symbol data
        logger.info(f"[LOAD] Loading data for {symbol} from {file_path}")
        symbol_df = pl.read_parquet(file_path)

        if len(symbol_df) < 20:
            logger.warning(f"[SKIP] Insufficient data for {symbol}: {len(symbol_df)} rows")
            return []

        # Process all strategies for this symbol
        all_results = []

        # Use asyncio for concurrent strategy processing
        semaphore = asyncio.Semaphore(CONCURRENT_STRATEGIES)

        async def process_strategy_async(strategy):
            async with semaphore:
                strategy_results = []
                for rr in RISK_REWARD_RATIOS:
                    result = await backtest_strategy_rr_async(symbol_df, strategy, timeframe, symbol, rr)
                    if result:
                        strategy_results.append(result)
                return strategy_results

        # Process strategies concurrently
        tasks = [process_strategy_async(strategy) for strategy in strategies]
        strategy_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Flatten results
        for result_list in strategy_results:
            if isinstance(result_list, list):
                all_results.extend(result_list)

        logger.info(f"[SUCCESS] Generated {len(all_results)} results for {symbol}")
        return all_results

    except Exception as e:
        logger.error(f"[ERROR] Failed to process {symbol}: {e}")
        return []

async def backtest_strategy_rr_async(symbol_df: pl.DataFrame, strategy: Dict[str, Any],
                                   timeframe: str, symbol: str, rr: List[float]) -> Optional[Dict[str, Any]]:
    """Async version of strategy backtesting"""
    try:
        # Run trade simulation
        trades = await simulate_trades_vectorized(symbol_df, strategy, rr, timeframe)

        if not trades:
            return None

        # Calculate performance metrics
        result = calculate_performance_metrics(trades, symbol, strategy['name'], timeframe, rr)
        return result

    except Exception as e:
        logger.error(f"Async backtest strategy R:R failed: {e}")
        return None

async def write_symbol_results_async(results: List[Dict[str, Any]], symbol: str, timeframe: str):
    """Write results for a single symbol to parquet file"""
    try:
        if not results:
            return

        # Create output filename
        output_filename = generate_output_filename(symbol, timeframe)
        output_path = Path(OUTPUT_DIR) / output_filename

        # Convert results to polars DataFrame
        results_df = pl.DataFrame(results)

        # Write to parquet with compression
        with _file_write_lock:
            results_df.write_parquet(
                output_path,
                compression=COMPRESSION,
                use_pyarrow=True
            )

        file_size = output_path.stat().st_size / (1024 * 1024)
        logger.info(f"[WRITE] Saved {len(results)} results for {symbol} to {output_filename} ({file_size:.1f} MB)")

    except Exception as e:
        logger.error(f"[ERROR] Error writing results for {symbol}: {e}")
    finally:
        # Force cleanup
        gc.collect()